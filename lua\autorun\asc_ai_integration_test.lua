-- Advanced Space Combat - AI Integration System Test
-- This file tests the specific InitializeSystemIntegrations function fix

if not ASC then ASC = {} end
ASC.AIIntegrationTest = {}

-- Test the specific function that was causing the error
function ASC.AIIntegrationTest.TestInitializeSystemIntegrations()
    print("[AI Integration Test] Testing InitializeSystemIntegrations function...")
    
    -- Check if ASC.AI exists
    if not ASC.AI then
        print("[AI Integration Test] ❌ ASC.AI not found!")
        return false
    end
    
    -- Check if AddonIntegration exists
    if not ASC.AI.AddonIntegration then
        print("[AI Integration Test] ❌ ASC.AI.AddonIntegration not found!")
        return false
    end
    
    -- Check if InitializeSystemIntegrations function exists
    if not ASC.AI.AddonIntegration.InitializeSystemIntegrations then
        print("[AI Integration Test] ❌ ASC.AI.AddonIntegration.InitializeSystemIntegrations not found!")
        return false
    end
    
    -- Check if it's a function
    if type(ASC.AI.AddonIntegration.InitializeSystemIntegrations) ~= "function" then
        print("[AI Integration Test] ❌ InitializeSystemIntegrations is not a function! Type: " .. type(ASC.AI.AddonIntegration.InitializeSystemIntegrations))
        return false
    end
    
    print("[AI Integration Test] ✅ InitializeSystemIntegrations function found and is callable!")
    
    -- Test calling the function (safely)
    local success, result = pcall(function()
        ASC.AI.AddonIntegration.InitializeSystemIntegrations()
        return true
    end)
    
    if success then
        print("[AI Integration Test] ✅ InitializeSystemIntegrations function executed successfully!")
        return true
    else
        print("[AI Integration Test] ❌ InitializeSystemIntegrations function failed to execute: " .. tostring(result))
        return false
    end
end

-- Test other critical AddonIntegration functions
function ASC.AIIntegrationTest.TestAddonIntegrationFunctions()
    print("[AI Integration Test] Testing other AddonIntegration functions...")
    
    local functions = {
        "IntegrateWithShipCore",
        "IntegrateWithWeapons", 
        "IntegrateWithCAPEntity",
        "IntegrateWithHyperdriveEngine",
        "IntegrateWithShieldSystem",
        "IntegrateWithTacticalAI",
        "IntegrateWithNavigationAI",
        "MonitorSystemPerformance",
        "SendSystemStatus",
        "CountAIEnhanced"
    }
    
    local passed = 0
    for _, funcName in ipairs(functions) do
        if ASC.AI.AddonIntegration[funcName] and type(ASC.AI.AddonIntegration[funcName]) == "function" then
            print("[AI Integration Test] ✅ " .. funcName .. " - OK")
            passed = passed + 1
        else
            print("[AI Integration Test] ❌ " .. funcName .. " - MISSING or NOT A FUNCTION")
        end
    end
    
    print("[AI Integration Test] AddonIntegration functions: " .. passed .. "/" .. #functions .. " found")
    return passed == #functions
end

-- Test the specific error scenario
function ASC.AIIntegrationTest.TestErrorScenario()
    print("[AI Integration Test] Testing the specific error scenario from line 7332...")
    
    -- Simulate the exact call that was failing
    local success, result = pcall(function()
        -- This is the exact line that was causing the error
        ASC.AI.AddonIntegration.InitializeSystemIntegrations()
        return true
    end)
    
    if success then
        print("[AI Integration Test] ✅ Error scenario test PASSED - No more 'attempt to call field InitializeSystemIntegrations (a nil value)' error!")
        return true
    else
        print("[AI Integration Test] ❌ Error scenario test FAILED - Error still occurs: " .. tostring(result))
        return false
    end
end

-- Test integration system hooks
function ASC.AIIntegrationTest.TestIntegrationHooks()
    print("[AI Integration Test] Testing integration system hooks...")
    
    local hooks = hook.GetTable()
    local expectedHooks = {
        "ASC_AI_EntityIntegration",
        "ASC_AI_TacticalIntegration", 
        "ASC_AI_ProactiveHelp"
    }
    
    local found = 0
    for _, hookName in ipairs(expectedHooks) do
        local hookFound = false
        
        -- Check in OnEntityCreated hooks
        if hooks.OnEntityCreated and hooks.OnEntityCreated[hookName] then
            hookFound = true
        end
        
        -- Check in TacticalAICreated hooks
        if hooks.TacticalAICreated and hooks.TacticalAICreated[hookName] then
            hookFound = true
        end
        
        -- Check in PlayerSpawnedSENT hooks
        if hooks.PlayerSpawnedSENT and hooks.PlayerSpawnedSENT[hookName] then
            hookFound = true
        end
        
        if hookFound then
            print("[AI Integration Test] ✅ Hook " .. hookName .. " - REGISTERED")
            found = found + 1
        else
            print("[AI Integration Test] ○ Hook " .. hookName .. " - NOT FOUND (may be registered later)")
        end
    end
    
    print("[AI Integration Test] Integration hooks: " .. found .. "/" .. #expectedHooks .. " found")
    return true -- Don't fail on this since hooks might be registered later
end

-- Test timer existence
function ASC.AIIntegrationTest.TestSystemMonitorTimer()
    print("[AI Integration Test] Testing system monitor timer...")
    
    -- Check if the timer exists
    if timer.Exists("ASC_AI_SystemMonitor") then
        print("[AI Integration Test] ✅ ASC_AI_SystemMonitor timer is running")
        return true
    else
        print("[AI Integration Test] ○ ASC_AI_SystemMonitor timer not found (may be created later)")
        return true -- Don't fail since timer might be created later
    end
end

-- Run all integration tests
function ASC.AIIntegrationTest.RunAllTests()
    print("[AI Integration Test] =====================================")
    print("[AI Integration Test] AI INTEGRATION SYSTEM TEST")
    print("[AI Integration Test] Testing the InitializeSystemIntegrations fix")
    print("[AI Integration Test] =====================================")
    
    local tests = {
        {name = "InitializeSystemIntegrations Function", func = ASC.AIIntegrationTest.TestInitializeSystemIntegrations},
        {name = "AddonIntegration Functions", func = ASC.AIIntegrationTest.TestAddonIntegrationFunctions},
        {name = "Error Scenario Fix", func = ASC.AIIntegrationTest.TestErrorScenario},
        {name = "Integration Hooks", func = ASC.AIIntegrationTest.TestIntegrationHooks},
        {name = "System Monitor Timer", func = ASC.AIIntegrationTest.TestSystemMonitorTimer}
    }
    
    local passed = 0
    local total = #tests
    
    for _, test in ipairs(tests) do
        print("[AI Integration Test] Running: " .. test.name)
        if test.func() then
            passed = passed + 1
        end
        print("[AI Integration Test] ---")
    end
    
    print("[AI Integration Test] =====================================")
    print("[AI Integration Test] RESULTS: " .. passed .. "/" .. total .. " tests passed")
    
    if passed == total then
        print("[AI Integration Test] 🎉 ALL TESTS PASSED!")
        print("[AI Integration Test] ✅ The InitializeSystemIntegrations error has been FIXED!")
        print("[AI Integration Test] 💡 AI system should now initialize without errors")
    else
        print("[AI Integration Test] ⚠️ Some tests failed!")
        print("[AI Integration Test] 🔧 Check the results above for details")
    end
    
    print("[AI Integration Test] =====================================")
    
    return passed == total
end

-- Console command to run integration tests
concommand.Add("asc_test_integration", function(ply, cmd, args)
    local success = ASC.AIIntegrationTest.RunAllTests()
    local msg = "[AI Integration Test] Integration Test " .. (success and "PASSED" or "FAILED")
    
    if IsValid(ply) then
        ply:ChatPrint(msg)
    else
        print(msg)
    end
end)

-- Quick test command for the specific fix
concommand.Add("asc_test_init_fix", function(ply, cmd, args)
    local success = ASC.AIIntegrationTest.TestErrorScenario()
    local msg = "[AI Integration Test] InitializeSystemIntegrations Fix " .. (success and "WORKING" or "FAILED")
    
    if IsValid(ply) then
        ply:ChatPrint(msg)
    else
        print(msg)
    end
end)

-- Auto-run test after a delay to ensure AI system is loaded
timer.Simple(2, function()
    print("[AI Integration Test] Auto-running integration tests...")
    ASC.AIIntegrationTest.RunAllTests()
end)

print("[Advanced Space Combat] AI Integration Test System loaded - Use 'asc_test_integration' to run tests")
