-- Advanced Space Combat - Manual AI System Test
-- This file provides manual testing commands for the AI system

if not ASC then ASC = {} end
ASC.AIManualTest = {}

-- Manual test functions
ASC.AIManualTest.TestBasicResponse = function(ply)
    if not IsValid(ply) then return end
    
    ply:Chat<PERSON><PERSON>t("[AI Manual Test] Testing basic AI response...")
    
    -- Test if AI system exists
    if not ASC.AI then
        ply:Cha<PERSON><PERSON><PERSON><PERSON>("[AI Manual Test] ❌ ASC.AI not found!")
        return false
    end
    
    if not ASC.AI.ProcessQuery then
        ply:Chat<PERSON>rint("[AI Manual Test] ❌ ASC.AI.ProcessQuery not found!")
        return false
    end
    
    -- Test basic query processing
    local success, result = pcall(function()
        ASC.AI.ProcessQuery(ply, "aria help")
        return true
    end)
    
    if success then
        ply:Chat<PERSON>rint("[AI Manual Test] ✅ Basic AI response test passed!")
        return true
    else
        ply:Chat<PERSON>rint("[AI Manual Test] ❌ Basic AI response test failed: " .. tostring(result))
        return false
    end
end

ASC.AIManualTest.TestNLPFunctions = function(ply)
    if not IsValid(ply) then return end
    
    ply:Chat<PERSON>rint("[AI Manual Test] Testing NLP functions...")
    
    if not ASC.AI.NLP then
        ply:ChatPrint("[AI Manual Test] ❌ ASC.AI.NLP not found!")
        return false
    end
    
    local success, result = pcall(function()
        local intent, confidence = ASC.AI.NLP.AnalyzeIntent("help me")
        local sentiment, score = ASC.AI.NLP.AnalyzeSentiment("please help")
        local contexts = ASC.AI.NLP.ExtractContext("ship status")
        
        return intent and confidence and sentiment and score and contexts
    end)
    
    if success and result then
        ply:ChatPrint("[AI Manual Test] ✅ NLP functions test passed!")
        return true
    else
        ply:ChatPrint("[AI Manual Test] ❌ NLP functions test failed: " .. tostring(result))
        return false
    end
end

ASC.AIManualTest.TestShipCoreDetection = function(ply)
    if not IsValid(ply) then return end
    
    ply:ChatPrint("[AI Manual Test] Testing ship core detection...")
    
    if not ASC.AI.FindPlayerShipCore then
        ply:ChatPrint("[AI Manual Test] ❌ ASC.AI.FindPlayerShipCore not found!")
        return false
    end
    
    local success, result = pcall(function()
        local shipCore = ASC.AI.FindPlayerShipCore(ply)
        return true -- Function exists and runs without error
    end)
    
    if success then
        ply:ChatPrint("[AI Manual Test] ✅ Ship core detection test passed!")
        return true
    else
        ply:ChatPrint("[AI Manual Test] ❌ Ship core detection test failed: " .. tostring(result))
        return false
    end
end

ASC.AIManualTest.TestChatCommands = function(ply)
    if not IsValid(ply) then return end
    
    ply:ChatPrint("[AI Manual Test] Testing chat command processing...")
    
    -- Check if chat hook exists
    local hooks = hook.GetTable()
    if hooks.PlayerSay and hooks.PlayerSay["ASC_AI_Chat"] then
        ply:ChatPrint("[AI Manual Test] ✅ Chat command hook found!")
        return true
    else
        ply:ChatPrint("[AI Manual Test] ❌ Chat command hook not found!")
        return false
    end
end

ASC.AIManualTest.TestConsoleCommands = function(ply)
    if not IsValid(ply) then return end
    
    ply:ChatPrint("[AI Manual Test] Testing console commands...")
    
    local commands = {"aria_test", "aria_reset", "aria_debug"}
    local found = 0
    
    for _, cmd in ipairs(commands) do
        if concommand.GetTable()[cmd] then
            found = found + 1
        end
    end
    
    ply:ChatPrint("[AI Manual Test] Console commands found: " .. found .. "/" .. #commands)
    
    if found == #commands then
        ply:ChatPrint("[AI Manual Test] ✅ All console commands found!")
        return true
    else
        ply:ChatPrint("[AI Manual Test] ⚠️ Some console commands missing!")
        return false
    end
end

ASC.AIManualTest.RunAllTests = function(ply)
    if not IsValid(ply) then return end
    
    ply:ChatPrint("[AI Manual Test] =====================================")
    ply:ChatPrint("[AI Manual Test] Running Manual AI System Tests...")
    ply:ChatPrint("[AI Manual Test] =====================================")
    
    local tests = {
        {name = "Basic Response", func = ASC.AIManualTest.TestBasicResponse},
        {name = "NLP Functions", func = ASC.AIManualTest.TestNLPFunctions},
        {name = "Ship Core Detection", func = ASC.AIManualTest.TestShipCoreDetection},
        {name = "Chat Commands", func = ASC.AIManualTest.TestChatCommands},
        {name = "Console Commands", func = ASC.AIManualTest.TestConsoleCommands}
    }
    
    local passed = 0
    local total = #tests
    
    for _, test in ipairs(tests) do
        ply:ChatPrint("[AI Manual Test] Running: " .. test.name)
        if test.func(ply) then
            passed = passed + 1
        end
        timer.Simple(0.5, function() end) -- Small delay between tests
    end
    
    timer.Simple(3, function()
        if IsValid(ply) then
            ply:ChatPrint("[AI Manual Test] =====================================")
            ply:ChatPrint("[AI Manual Test] RESULTS: " .. passed .. "/" .. total .. " tests passed")
            
            if passed == total then
                ply:ChatPrint("[AI Manual Test] 🎉 ALL TESTS PASSED!")
                ply:ChatPrint("[AI Manual Test] 💡 AI System is working correctly!")
                ply:ChatPrint("[AI Manual Test] Try: 'aria help' or 'aria status'")
            else
                ply:ChatPrint("[AI Manual Test] ⚠️ Some tests failed!")
                ply:ChatPrint("[AI Manual Test] 🔧 Check console for detailed errors")
            end
            ply:ChatPrint("[AI Manual Test] =====================================")
        end
    end)
end

-- Console commands for manual testing
concommand.Add("asc_ai_test_basic", function(ply, cmd, args)
    ASC.AIManualTest.TestBasicResponse(ply)
end)

concommand.Add("asc_ai_test_nlp", function(ply, cmd, args)
    ASC.AIManualTest.TestNLPFunctions(ply)
end)

concommand.Add("asc_ai_test_ship", function(ply, cmd, args)
    ASC.AIManualTest.TestShipCoreDetection(ply)
end)

concommand.Add("asc_ai_test_chat", function(ply, cmd, args)
    ASC.AIManualTest.TestChatCommands(ply)
end)

concommand.Add("asc_ai_test_console", function(ply, cmd, args)
    ASC.AIManualTest.TestConsoleCommands(ply)
end)

concommand.Add("asc_ai_test_all", function(ply, cmd, args)
    ASC.AIManualTest.RunAllTests(ply)
end)

-- Quick AI status check
concommand.Add("asc_ai_quick_status", function(ply, cmd, args)
    if not IsValid(ply) then return end
    
    ply:ChatPrint("[AI Status] =====================================")
    ply:ChatPrint("[AI Status] ARIA-4 AI System Quick Status Check")
    ply:ChatPrint("[AI Status] =====================================")
    
    -- Check basic components
    ply:ChatPrint("[AI Status] ASC.AI exists: " .. (ASC.AI and "✅ YES" or "❌ NO"))
    ply:ChatPrint("[AI Status] ProcessQuery exists: " .. (ASC.AI and ASC.AI.ProcessQuery and "✅ YES" or "❌ NO"))
    ply:ChatPrint("[AI Status] NLP module exists: " .. (ASC.AI and ASC.AI.NLP and "✅ YES" or "❌ NO"))
    ply:ChatPrint("[AI Status] Chat hook registered: " .. (hook.GetTable().PlayerSay and hook.GetTable().PlayerSay["ASC_AI_Chat"] and "✅ YES" or "❌ NO"))
    
    -- Check data structures
    ply:ChatPrint("[AI Status] User profiles: " .. (ASC.AI and ASC.AI.UserProfiles and table.Count(ASC.AI.UserProfiles) or "0") .. " stored")
    ply:ChatPrint("[AI Status] Conversation history: " .. (ASC.AI and ASC.AI.ConversationHistory and table.Count(ASC.AI.ConversationHistory) or "0") .. " players")
    
    -- Memory usage
    ply:ChatPrint("[AI Status] Memory usage: " .. math.floor(collectgarbage("count")) .. " KB")
    
    ply:ChatPrint("[AI Status] =====================================")
    
    if ASC.AI and ASC.AI.ProcessQuery and ASC.AI.NLP then
        ply:ChatPrint("[AI Status] 🎉 AI System appears to be working!")
        ply:ChatPrint("[AI Status] 💡 Try: 'aria help' to test AI responses")
    else
        ply:ChatPrint("[AI Status] ⚠️ AI System has issues!")
        ply:ChatPrint("[AI Status] 🔧 Use 'asc_ai_test_all' for detailed testing")
    end
end)

print("[Advanced Space Combat] AI Manual Test System loaded")
print("[Advanced Space Combat] Commands: asc_ai_test_all, asc_ai_quick_status, asc_ai_test_basic, etc.")
