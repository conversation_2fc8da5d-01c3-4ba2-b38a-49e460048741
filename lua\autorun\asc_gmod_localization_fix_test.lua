--[[
    Advanced Space Combat - GMod Localization Fix Test v1.0.0
    
    Quick test specifically for the GMod localization language global error fix.
    This addresses the "attempt to index global 'language' (a nil value)" error.
]]

-- Initialize GMod Localization Fix Test namespace
ASC = ASC or {}
ASC.GModLocalizationFixTest = ASC.GModLocalizationFixTest or {}

-- Test the specific GMod localization fix
function ASC.GModLocalizationFixTest.TestLanguageGlobalFix()
    print("[GMod Localization Fix Test] Testing GMod localization language global fix...")
    
    local testResults = {
        passed = 0,
        failed = 0,
        errors = {},
        warnings = {}
    }
    
    -- Test 1: Check if GMod localization system exists
    if not ASC.GMod or not ASC.GMod.Localization then
        testResults.failed = testResults.failed + 1
        table.insert(testResults.errors, "GMod localization system not found")
        return false, testResults
    end
    
    testResults.passed = testResults.passed + 1
    print("[GMod Localization Fix Test] ✓ GMod localization system found")
    
    -- Test 2: Test GetText function that was causing the language global error
    if ASC.GMod.Localization.GetText then
        local success, result = pcall(function()
            return ASC.GMod.Localization.GetText("test.key", "fallback")
        end)
        
        if success then
            testResults.passed = testResults.passed + 1
            print("[GMod Localization Fix Test] ✓ GetText function runs without language global error")
        else
            testResults.failed = testResults.failed + 1
            table.insert(testResults.errors, "GetText still fails: " .. tostring(result))
        end
    else
        testResults.failed = testResults.failed + 1
        table.insert(testResults.errors, "GetText function not found")
    end
    
    -- Test 3: Test AddTranslation function
    if ASC.GMod.Localization.AddTranslation then
        local success, result = pcall(function()
            return ASC.GMod.Localization.AddTranslation("test.key", "test value", "en")
        end)
        
        if success then
            testResults.passed = testResults.passed + 1
            print("[GMod Localization Fix Test] ✓ AddTranslation function runs without language global error")
        else
            testResults.failed = testResults.failed + 1
            table.insert(testResults.errors, "AddTranslation still fails: " .. tostring(result))
        end
    else
        testResults.failed = testResults.failed + 1
        table.insert(testResults.errors, "AddTranslation function not found")
    end
    
    -- Test 4: Test IntegrateWithCzechSystem function
    if ASC.GMod.Localization.IntegrateWithCzechSystem then
        local success, result = pcall(function()
            return ASC.GMod.Localization.IntegrateWithCzechSystem()
        end)
        
        if success then
            testResults.passed = testResults.passed + 1
            print("[GMod Localization Fix Test] ✓ IntegrateWithCzechSystem runs without errors")
        else
            testResults.failed = testResults.failed + 1
            table.insert(testResults.errors, "IntegrateWithCzechSystem still fails: " .. tostring(result))
        end
    else
        testResults.failed = testResults.failed + 1
        table.insert(testResults.errors, "IntegrateWithCzechSystem function not found")
    end
    
    -- Test 5: Test direct language global access safety
    local languageAccessTests = {
        {
            name = "Direct _G.language access",
            test = function()
                local success, result = pcall(function()
                    if _G.language and _G.language.GetPhrase then
                        return _G.language.GetPhrase("test")
                    end
                    return "safe"
                end)
                return success
            end
        },
        {
            name = "Language global existence check",
            test = function()
                -- This should not error even if language is nil
                local success, result = pcall(function()
                    local exists = (_G.language ~= nil)
                    return exists
                end)
                return success
            end
        }
    }
    
    for _, test in ipairs(languageAccessTests) do
        local success = test.test()
        if success then
            testResults.passed = testResults.passed + 1
            print("[GMod Localization Fix Test] ✓ " .. test.name .. " - safe")
        else
            testResults.failed = testResults.failed + 1
            table.insert(testResults.errors, test.name .. " - still has issues")
        end
    end
    
    local allPassed = testResults.failed == 0
    print("[GMod Localization Fix Test] GMod Localization Fix: " .. (allPassed and "✓ WORKING" or "✗ FAILED"))
    
    return allPassed, testResults
end

-- Run comprehensive GMod localization fix test
function ASC.GModLocalizationFixTest.RunAllTests()
    print("[GMod Localization Fix Test] ==========================================")
    print("[GMod Localization Fix Test] GMod Localization Fix Test")
    print("[GMod Localization Fix Test] ==========================================")
    
    local startTime = CurTime()
    
    -- Run the main test
    local success, results = ASC.GModLocalizationFixTest.TestLanguageGlobalFix()
    
    local testTime = CurTime() - startTime
    
    -- Print summary
    print("[GMod Localization Fix Test] ==========================================")
    print("[GMod Localization Fix Test] TEST SUMMARY")
    print("[GMod Localization Fix Test] ==========================================")
    print("[GMod Localization Fix Test] Overall Status: " .. (success and "✅ ALL TESTS PASSED" or "❌ SOME TESTS FAILED"))
    print("[GMod Localization Fix Test] Tests Passed: " .. results.passed)
    print("[GMod Localization Fix Test] Tests Failed: " .. results.failed)
    print("[GMod Localization Fix Test] Test Duration: " .. string.format("%.3fs", testTime))
    
    if #results.errors > 0 then
        print("[GMod Localization Fix Test] ERRORS:")
        for _, error in ipairs(results.errors) do
            print("[GMod Localization Fix Test]   - " .. error)
        end
    end
    
    if #results.warnings > 0 then
        print("[GMod Localization Fix Test] WARNINGS:")
        for _, warning in ipairs(results.warnings) do
            print("[GMod Localization Fix Test]   - " .. warning)
        end
    end
    
    if success then
        print("[GMod Localization Fix Test] 🎉 GMod localization language global error has been FIXED!")
        print("[GMod Localization Fix Test] The system now safely handles language global access.")
    else
        print("[GMod Localization Fix Test] ❌ GMod localization fix needs more work.")
    end
    
    print("[GMod Localization Fix Test] ==========================================")
    
    return success
end

-- Console command for testing the GMod localization fix
concommand.Add("asc_test_gmod_localization_fix", function(ply, cmd, args)
    local success = ASC.GModLocalizationFixTest.RunAllTests()
    
    local msg = "[GMod Localization Fix Test] " .. (success and "✅ GMod localization fix is working!" or "❌ GMod localization fix has issues")
    
    if IsValid(ply) then
        ply:ChatPrint(msg)
    else
        print(msg)
    end
end)

-- Quick status command
concommand.Add("asc_gmod_localization_fix_status", function(ply, cmd, args)
    local msg = "[GMod Localization Fix Test] Testing GMod localization fix..."
    
    if IsValid(ply) then
        ply:ChatPrint(msg)
    else
        print(msg)
    end
    
    -- Run quick test
    local success, results = ASC.GModLocalizationFixTest.TestLanguageGlobalFix()
    
    local statusMsg = "[GMod Localization Fix Test] Status: " .. (success and "✅ WORKING" or "❌ FAILED") .. 
                     " (" .. results.passed .. " passed, " .. results.failed .. " failed)"
    
    if IsValid(ply) then
        ply:ChatPrint(statusMsg)
    else
        print(statusMsg)
    end
end)

-- Auto-test on load
timer.Simple(5, function()
    print("[GMod Localization Fix Test] Running automatic GMod localization fix test...")
    local success = ASC.GModLocalizationFixTest.RunAllTests()
    
    if success then
        print("[GMod Localization Fix Test] ✅ Auto-test PASSED - GMod localization fix is working!")
    else
        print("[GMod Localization Fix Test] ❌ Auto-test FAILED - GMod localization fix needs attention!")
    end
end)

print("[Advanced Space Combat] GMod Localization Fix Test v1.0.0 Loaded")
