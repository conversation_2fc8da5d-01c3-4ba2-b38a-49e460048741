-- Advanced Space Combat - Language Fix Verification
-- This file tests the language global fixes to ensure no more "attempt to index global 'language' (a nil value)" errors

if not ASC then ASC = {} end
ASC.LanguageFixVerification = {}

-- Test configuration
ASC.LanguageFixVerification.Config = {
    RunOnStartup = true,
    LogResults = true
}

-- Test results storage
ASC.LanguageFixVerification.Results = {
    TestsPassed = 0,
    TestsFailed = 0,
    Errors = {}
}

-- Test 1: Test localization test file fix
function ASC.LanguageFixVerification.TestLocalizationTestFix()
    print("[Language Fix Verification] Testing localization test file fix...")
    
    local success, result = pcall(function()
        if ASC.LocalizationTest and ASC.LocalizationTest.TestTranslations then
            -- This should not error anymore
            return ASC.LocalizationTest.TestTranslations()
        end
        return true
    end)
    
    if success then
        ASC.LanguageFixVerification.Results.TestsPassed = ASC.LanguageFixVerification.Results.TestsPassed + 1
        print("[Language Fix Verification] ✓ Localization test fix working")
        return true
    else
        ASC.LanguageFixVerification.Results.TestsFailed = ASC.LanguageFixVerification.Results.TestsFailed + 1
        table.insert(ASC.LanguageFixVerification.Results.Errors, "Localization test still fails: " .. tostring(result))
        print("[Language Fix Verification] ✗ Localization test still fails: " .. tostring(result))
        return false
    end
end

-- Test 2: Test UI system fix
function ASC.LanguageFixVerification.TestUISystemFix()
    print("[Language Fix Verification] Testing UI system fix...")
    
    local success, result = pcall(function()
        if ASC.UI and ASC.UI.GetText then
            -- This should not error anymore
            return ASC.UI.GetText("test_key", "fallback")
        end
        return "safe"
    end)
    
    if success then
        ASC.LanguageFixVerification.Results.TestsPassed = ASC.LanguageFixVerification.Results.TestsPassed + 1
        print("[Language Fix Verification] ✓ UI system fix working")
        return true
    else
        ASC.LanguageFixVerification.Results.TestsFailed = ASC.LanguageFixVerification.Results.TestsFailed + 1
        table.insert(ASC.LanguageFixVerification.Results.Errors, "UI system still fails: " .. tostring(result))
        print("[Language Fix Verification] ✗ UI system still fails: " .. tostring(result))
        return false
    end
end

-- Test 3: Test console commands fix
function ASC.LanguageFixVerification.TestConsoleCommandsFix()
    print("[Language Fix Verification] Testing console commands fix...")
    
    local success, result = pcall(function()
        if ASC.Commands and ASC.Commands.Registry then
            -- Test the GetText function used in commands
            for name, cmdData in pairs(ASC.Commands.Registry) do
                -- This should not error anymore
                break
            end
        end
        return true
    end)
    
    if success then
        ASC.LanguageFixVerification.Results.TestsPassed = ASC.LanguageFixVerification.Results.TestsPassed + 1
        print("[Language Fix Verification] ✓ Console commands fix working")
        return true
    else
        ASC.LanguageFixVerification.Results.TestsFailed = ASC.LanguageFixVerification.Results.TestsFailed + 1
        table.insert(ASC.LanguageFixVerification.Results.Errors, "Console commands still fail: " .. tostring(result))
        print("[Language Fix Verification] ✗ Console commands still fail: " .. tostring(result))
        return false
    end
end

-- Test 4: Test GMod localization fix
function ASC.LanguageFixVerification.TestGModLocalizationFix()
    print("[Language Fix Verification] Testing GMod localization fix...")
    
    local success, result = pcall(function()
        if ASC.GMod and ASC.GMod.Localization then
            -- Test GetText function
            local text = ASC.GMod.Localization.GetText("test.key", "fallback")
            
            -- Test SetLanguage function
            ASC.GMod.Localization.SetLanguage("en")
            
            return true
        end
        return true
    end)
    
    if success then
        ASC.LanguageFixVerification.Results.TestsPassed = ASC.LanguageFixVerification.Results.TestsPassed + 1
        print("[Language Fix Verification] ✓ GMod localization fix working")
        return true
    else
        ASC.LanguageFixVerification.Results.TestsFailed = ASC.LanguageFixVerification.Results.TestsFailed + 1
        table.insert(ASC.LanguageFixVerification.Results.Errors, "GMod localization still fails: " .. tostring(result))
        print("[Language Fix Verification] ✗ GMod localization still fails: " .. tostring(result))
        return false
    end
end

-- Test 5: Test direct language global access safety
function ASC.LanguageFixVerification.TestLanguageGlobalSafety()
    print("[Language Fix Verification] Testing language global access safety...")
    
    local tests = {
        {
            name = "Direct _G.language access",
            test = function()
                local success, result = pcall(function()
                    if _G.language and _G.language.GetPhrase then
                        return _G.language.GetPhrase("test")
                    end
                    return "safe"
                end)
                return success
            end
        },
        {
            name = "Language global existence check",
            test = function()
                local success, result = pcall(function()
                    local exists = (_G.language ~= nil)
                    return exists
                end)
                return success
            end
        }
    }
    
    local allPassed = true
    for _, test in ipairs(tests) do
        if test.test() then
            print("[Language Fix Verification] ✓ " .. test.name .. " passed")
        else
            print("[Language Fix Verification] ✗ " .. test.name .. " failed")
            allPassed = false
        end
    end
    
    if allPassed then
        ASC.LanguageFixVerification.Results.TestsPassed = ASC.LanguageFixVerification.Results.TestsPassed + 1
        return true
    else
        ASC.LanguageFixVerification.Results.TestsFailed = ASC.LanguageFixVerification.Results.TestsFailed + 1
        table.insert(ASC.LanguageFixVerification.Results.Errors, "Language global safety tests failed")
        return false
    end
end

-- Run all verification tests
function ASC.LanguageFixVerification.RunAllTests()
    print("[Language Fix Verification] Running language fix verification tests...")
    print("[Language Fix Verification] =====================================")
    
    -- Reset results
    ASC.LanguageFixVerification.Results.TestsPassed = 0
    ASC.LanguageFixVerification.Results.TestsFailed = 0
    ASC.LanguageFixVerification.Results.Errors = {}
    
    -- Run tests
    ASC.LanguageFixVerification.TestLocalizationTestFix()
    ASC.LanguageFixVerification.TestUISystemFix()
    ASC.LanguageFixVerification.TestConsoleCommandsFix()
    ASC.LanguageFixVerification.TestGModLocalizationFix()
    ASC.LanguageFixVerification.TestLanguageGlobalSafety()
    
    -- Show results
    local totalTests = ASC.LanguageFixVerification.Results.TestsPassed + ASC.LanguageFixVerification.Results.TestsFailed
    local successRate = totalTests > 0 and (ASC.LanguageFixVerification.Results.TestsPassed / totalTests * 100) or 0
    
    print("[Language Fix Verification] =====================================")
    print("[Language Fix Verification] VERIFICATION COMPLETE")
    print("[Language Fix Verification] Tests Passed: " .. ASC.LanguageFixVerification.Results.TestsPassed)
    print("[Language Fix Verification] Tests Failed: " .. ASC.LanguageFixVerification.Results.TestsFailed)
    print("[Language Fix Verification] Success Rate: " .. math.Round(successRate, 1) .. "%")
    
    if #ASC.LanguageFixVerification.Results.Errors > 0 then
        print("[Language Fix Verification] Errors:")
        for _, error in ipairs(ASC.LanguageFixVerification.Results.Errors) do
            print("[Language Fix Verification] • " .. error)
        end
    end
    
    print("[Language Fix Verification] =====================================")
    
    return ASC.LanguageFixVerification.Results.TestsFailed == 0
end

-- Console command to run verification
concommand.Add("asc_verify_language_fixes", function(ply, cmd, args)
    local success = ASC.LanguageFixVerification.RunAllTests()
    local msg = "[Language Fix Verification] Verification " .. (success and "PASSED" or "FAILED")
    
    if IsValid(ply) then
        ply:ChatPrint(msg)
    else
        print(msg)
    end
end)

-- Auto-run verification if enabled
if ASC.LanguageFixVerification.Config.RunOnStartup then
    timer.Simple(5, function()
        ASC.LanguageFixVerification.RunAllTests()
    end)
end

print("[Advanced Space Combat] Language Fix Verification System loaded")
