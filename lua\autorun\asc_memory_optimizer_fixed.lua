-- Advanced Space Combat - Memory Optimizer (Fixed Version)
-- Optimizes memory usage and prevents memory leaks

if not ASC then ASC = {} end

ASC.MemoryOptimizer = {
    Version = "1.0.0",
    Initialized = false,
    
    Config = {
        Enabled = true,
        GCInterval = 60, -- Increased from 30 to reduce frequency
        MemoryThreshold = 300, -- Increased threshold to reduce alerts
        CleanupInterval = 120, -- Increased from 60 to reduce frequency
        MaxCacheSize = 100,
        DebugMode = false,
        SilentMode = true -- Reduce console output
    },
    
    State = {
        LastGC = 0,
        LastCleanup = 0,
        MemoryUsage = 0,
        PeakMemoryUsage = 0,
        GCCount = 0,
        CleanupCount = 0
    },
    
    Caches = {
        EntityCache = {},
        MaterialCache = {},
        SoundCache = {},
        EffectCache = {},
        NetworkCache = {}
    },
    
    Pools = {
        Vectors = {},
        Angles = {},
        Tables = {}
    }
}

function ASC.MemoryOptimizer.GetMemoryUsage()
    return collectgarbage("count") / 1024
end

function ASC.MemoryOptimizer.PerformGC()
    local beforeMem = ASC.MemoryOptimizer.GetMemoryUsage()
    collectgarbage("collect")
    local afterMem = ASC.MemoryOptimizer.GetMemoryUsage()
    
    ASC.MemoryOptimizer.State.GCCount = ASC.MemoryOptimizer.State.GCCount + 1
    ASC.MemoryOptimizer.State.LastGC = CurTime()
    
    local freed = beforeMem - afterMem
    if ASC.MemoryOptimizer.Config.DebugMode then
        print("[ASC Memory] GC freed " .. string.format("%.2f", freed) .. "MB")
    end
    
    return freed
end

function ASC.MemoryOptimizer.CleanupCaches()
    local cleaned = 0
    
    for cacheName, cache in pairs(ASC.MemoryOptimizer.Caches) do
        local count = 0
        for k, v in pairs(cache) do
            if type(v) == "Entity" and not IsValid(v) then
                cache[k] = nil
                count = count + 1
            elseif type(v) == "table" and v.lastAccess and 
                   (CurTime() - v.lastAccess) > 300 then
                cache[k] = nil
                count = count + 1
            end
        end
        
        local cacheSize = table.Count(cache)
        if cacheSize > ASC.MemoryOptimizer.Config.MaxCacheSize then
            local toRemove = cacheSize - ASC.MemoryOptimizer.Config.MaxCacheSize
            local removed = 0
            for k, v in pairs(cache) do
                if removed >= toRemove then break end
                cache[k] = nil
                removed = removed + 1
                count = count + 1
            end
        end
        
        cleaned = cleaned + count
    end
    
    ASC.MemoryOptimizer.State.CleanupCount = ASC.MemoryOptimizer.State.CleanupCount + 1
    ASC.MemoryOptimizer.State.LastCleanup = CurTime()
    
    if ASC.MemoryOptimizer.Config.DebugMode and cleaned > 0 then
        print("[ASC Memory] Cleaned " .. cleaned .. " cache entries")
    end
    
    return cleaned
end

function ASC.MemoryOptimizer.EmergencyCleanup()
    print("[ASC Memory] Performing emergency cleanup...")
    
    local cleaned = 0
    
    for cacheName, cache in pairs(ASC.MemoryOptimizer.Caches) do
        local count = table.Count(cache)
        for k, v in pairs(cache) do
            cache[k] = nil
        end
        cleaned = cleaned + count
        print("[ASC Memory] Cleared " .. count .. " entries from " .. cacheName)
    end
    
    for poolName, pool in pairs(ASC.MemoryOptimizer.Pools) do
        local count = #pool
        for i = #pool, 1, -1 do
            pool[i] = nil
        end
        cleaned = cleaned + count
        print("[ASC Memory] Cleared " .. count .. " objects from " .. poolName .. " pool")
    end
    
    if ASC and ASC.Cache then
        for k, v in pairs(ASC.Cache) do
            ASC.Cache[k] = nil
            cleaned = cleaned + 1
        end
    end
    
    if ASC and ASC.AI and ASC.AI.ConversationHistory then
        for playerID, history in pairs(ASC.AI.ConversationHistory) do
            if #history > 10 then
                for i = #history, 11, -1 do
                    table.remove(history, i)
                    cleaned = cleaned + 1
                end
            end
        end
    end
    
    print("[ASC Memory] Emergency cleanup complete: " .. cleaned .. " items cleared")
    return cleaned
end

function ASC.MemoryOptimizer.Update()
    if not ASC.MemoryOptimizer.Config.Enabled then return end
    
    local currentTime = CurTime()
    local memUsage = ASC.MemoryOptimizer.GetMemoryUsage()
    local previousMemUsage = ASC.MemoryOptimizer.State.MemoryUsage or 0
    
    local memoryGrowth = 0
    if previousMemUsage > 0 then
        memoryGrowth = ((memUsage - previousMemUsage) / previousMemUsage) * 100
    end
    
    ASC.MemoryOptimizer.State.MemoryUsage = memUsage
    if memUsage > ASC.MemoryOptimizer.State.PeakMemoryUsage then
        ASC.MemoryOptimizer.State.PeakMemoryUsage = memUsage
    end
    
    if memoryGrowth > 1000 then
        print("[ASC Memory] WARNING: Excessive memory growth detected: " .. string.format("%.1f", memoryGrowth) .. "%")
        print("[ASC Memory] Previous: " .. string.format("%.2f", previousMemUsage) .. "MB, Current: " .. string.format("%.2f", memUsage) .. "MB")
        
        ASC.MemoryOptimizer.EmergencyCleanup()
        
        for i = 1, 3 do
            collectgarbage("collect")
        end
        
        memUsage = ASC.MemoryOptimizer.GetMemoryUsage()
        print("[ASC Memory] After emergency cleanup: " .. string.format("%.2f", memUsage) .. "MB")
    end
    
    if currentTime - ASC.MemoryOptimizer.State.LastGC > ASC.MemoryOptimizer.Config.GCInterval then
        ASC.MemoryOptimizer.PerformGC()
    end
    
    if memUsage > ASC.MemoryOptimizer.Config.MemoryThreshold then
        ASC.MemoryOptimizer.PerformGC()
        if ASC.MemoryOptimizer.Config.DebugMode then
            print("[ASC Memory] Forced GC due to high memory usage: " .. string.format("%.2f", memUsage) .. "MB")
        end
    end
    
    if currentTime - ASC.MemoryOptimizer.State.LastCleanup > ASC.MemoryOptimizer.Config.CleanupInterval then
        ASC.MemoryOptimizer.CleanupCaches()
    end
end

function ASC.MemoryOptimizer.GetStats()
    return {
        CurrentMemoryMB = ASC.MemoryOptimizer.State.MemoryUsage,
        PeakMemoryMB = ASC.MemoryOptimizer.State.PeakMemoryUsage,
        GCCount = ASC.MemoryOptimizer.State.GCCount,
        CleanupCount = ASC.MemoryOptimizer.State.CleanupCount,
        CacheSizes = {
            Entity = table.Count(ASC.MemoryOptimizer.Caches.EntityCache),
            Material = table.Count(ASC.MemoryOptimizer.Caches.MaterialCache),
            Sound = table.Count(ASC.MemoryOptimizer.Caches.SoundCache),
            Effect = table.Count(ASC.MemoryOptimizer.Caches.EffectCache),
            Network = table.Count(ASC.MemoryOptimizer.Caches.NetworkCache)
        },
        PoolSizes = {
            Vectors = #ASC.MemoryOptimizer.Pools.Vectors,
            Angles = #ASC.MemoryOptimizer.Pools.Angles,
            Tables = #ASC.MemoryOptimizer.Pools.Tables
        }
    }
end

function ASC.MemoryOptimizer.Initialize()
    if ASC.MemoryOptimizer.Initialized then return end
    
    print("[ASC Memory] Initializing Memory Optimizer...")
    
    timer.Simple(10, function()
        if ASC and ASC.MasterScheduler then
            ASC.MasterScheduler.RegisterTask("ASC_MemoryOptimizer", "Low", function()
                ASC.MemoryOptimizer.Update()
            end, 5.0)
        else
            timer.Create("ASC_MemoryOptimizer", 5, 0, function()
                ASC.MemoryOptimizer.Update()
            end)
        end
    end)
    
    ASC.MemoryOptimizer.Initialized = true
    print("[ASC Memory] Memory Optimizer initialized successfully!")
end

concommand.Add("asc_memory_stats", function(ply, cmd, args)
    if IsValid(ply) and not ply:IsSuperAdmin() then return end
    
    local stats = ASC.MemoryOptimizer.GetStats()
    print("[ASC Memory] Memory Statistics:")
    print("  Current Memory: " .. string.format("%.2f", stats.CurrentMemoryMB) .. "MB")
    print("  Peak Memory: " .. string.format("%.2f", stats.PeakMemoryMB) .. "MB")
    print("  GC Count: " .. stats.GCCount)
    print("  Cleanup Count: " .. stats.CleanupCount)
end)

timer.Simple(2, function()
    ASC.MemoryOptimizer.Initialize()
end)

print("[Advanced Space Combat] Memory Optimizer (Fixed) loaded successfully!")
