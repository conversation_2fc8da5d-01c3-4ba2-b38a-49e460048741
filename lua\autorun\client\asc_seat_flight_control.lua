-- ASC Seat Flight Control - Client Side
-- Enhanced flight control system for seat-based ship piloting

if SERVER then return end

ASC = ASC or {}
ASC.SeatFlight = ASC.SeatFlight or {}

-- Client-side flight control state
ASC.SeatFlight.State = {
    active = false,
    shipCore = nil,
    keyBindings = {},
    sensitivity = {},
    inputState = {},
    mouseInput = {x = 0, y = 0},
    lastInputTime = 0,
    inputUpdateRate = 0.05, -- 20 FPS input updates
    nextInputUpdate = 0
}

-- Initialize seat flight control
function ASC.SeatFlight.Initialize()
    print("[ASC Seat Flight] Client-side seat flight control initialized")
    
    -- Set up input hooks
    ASC.SeatFlight.SetupInputHooks()
    
    -- Set up think hook for input processing
    hook.Add("Think", "ASC_SeatFlightThink", ASC.SeatFlight.Think)
end

-- Set up input hooks
function ASC.SeatFlight.SetupInputHooks()
    -- Mouse input hook
    hook.Add("InputMouseApply", "ASC_SeatFlightMouse", function(cmd, x, y, angle)
        if not ASC.SeatFlight.State.active then return end
        
        -- Store mouse input
        ASC.SeatFlight.State.mouseInput.x = x * 0.01 -- Scale down mouse sensitivity
        ASC.SeatFlight.State.mouseInput.y = y * 0.01
        
        -- Prevent normal mouse look when controlling ship
        return true
    end)
    
    -- Key input processing
    hook.Add("CreateMove", "ASC_SeatFlightKeys", function(cmd)
        if not ASC.SeatFlight.State.active then return end
        
        ASC.SeatFlight.ProcessKeyInputs(cmd)
    end)
end

-- Process keyboard inputs
function ASC.SeatFlight.ProcessKeyInputs(cmd)
    local state = ASC.SeatFlight.State
    local bindings = state.keyBindings
    
    -- Reset input state
    for key, _ in pairs(state.inputState) do
        state.inputState[key] = 0
    end
    
    -- Check movement keys
    if input.IsKeyDown(bindings.forward or KEY_W) then
        state.inputState.forward = 1
    end
    if input.IsKeyDown(bindings.backward or KEY_S) then
        state.inputState.backward = 1
    end
    if input.IsKeyDown(bindings.left or KEY_A) then
        state.inputState.left = 1
    end
    if input.IsKeyDown(bindings.right or KEY_D) then
        state.inputState.right = 1
    end
    if input.IsKeyDown(bindings.up or KEY_SPACE) then
        state.inputState.up = 1
    end
    if input.IsKeyDown(bindings.down or KEY_LCONTROL) then
        state.inputState.down = 1
    end
    
    -- Check modifier keys
    state.inputState.boost = input.IsKeyDown(bindings.boost or KEY_LSHIFT)
    
    -- Check command keys (only trigger once per press)
    if input.IsKeyDown(bindings.brake or KEY_X) and not state.inputState.brakePressed then
        state.inputState.brake = true
        state.inputState.brakePressed = true
    elseif not input.IsKeyDown(bindings.brake or KEY_X) then
        state.inputState.brakePressed = false
        state.inputState.brake = false
    end
    
    if input.IsKeyDown(bindings.autopilot or KEY_R) and not state.inputState.autopilotPressed then
        state.inputState.autopilot = true
        state.inputState.autopilotPressed = true
    elseif not input.IsKeyDown(bindings.autopilot or KEY_R) then
        state.inputState.autopilotPressed = false
        state.inputState.autopilot = false
    end
    
    if input.IsKeyDown(bindings.autoLevel or KEY_T) and not state.inputState.autoLevelPressed then
        state.inputState.autoLevel = true
        state.inputState.autoLevelPressed = true
    elseif not input.IsKeyDown(bindings.autoLevel or KEY_T) then
        state.inputState.autoLevelPressed = false
        state.inputState.autoLevel = false
    end
    
    -- Add mouse input
    state.inputState.mouseX = state.mouseInput.x
    state.inputState.mouseY = state.mouseInput.y
    
    -- Reset mouse input after reading
    state.mouseInput.x = 0
    state.mouseInput.y = 0
end

-- Think function for input updates
function ASC.SeatFlight.Think()
    if not ASC.SeatFlight.State.active then return end
    
    local currentTime = CurTime()
    if currentTime < ASC.SeatFlight.State.nextInputUpdate then return end
    
    ASC.SeatFlight.State.nextInputUpdate = currentTime + ASC.SeatFlight.State.inputUpdateRate
    ASC.SeatFlight.SendInputToServer()
end

-- Send input data to server
function ASC.SeatFlight.SendInputToServer()
    if not ASC.SeatFlight.State.active or not IsValid(ASC.SeatFlight.State.shipCore) then return end
    
    net.Start("asc_seat_flight_input")
    net.WriteTable(ASC.SeatFlight.State.inputState)
    net.SendToServer()
    
    ASC.SeatFlight.State.lastInputTime = CurTime()
end

-- Activate seat flight control
function ASC.SeatFlight.Activate(shipCore, keyBindings, sensitivity)
    ASC.SeatFlight.State.active = true
    ASC.SeatFlight.State.shipCore = shipCore
    ASC.SeatFlight.State.keyBindings = keyBindings or {}
    ASC.SeatFlight.State.sensitivity = sensitivity or {}
    ASC.SeatFlight.State.inputState = {}
    
    -- Show HUD notification
    ASC.SeatFlight.ShowControlHUD(true)
    
    print("[ASC Seat Flight] Flight control activated")
end

-- Deactivate seat flight control
function ASC.SeatFlight.Deactivate()
    ASC.SeatFlight.State.active = false
    ASC.SeatFlight.State.shipCore = nil
    ASC.SeatFlight.State.inputState = {}
    
    -- Hide HUD notification
    ASC.SeatFlight.ShowControlHUD(false)
    
    print("[ASC Seat Flight] Flight control deactivated")
end

-- Show/hide control HUD
function ASC.SeatFlight.ShowControlHUD(show)
    if show then
        -- Create HUD display
        hook.Add("HUDPaint", "ASC_SeatFlightHUD", ASC.SeatFlight.DrawHUD)
        
        -- Show control instructions
        chat.AddText(Color(100, 255, 100), "[ASC Flight] ", Color(255, 255, 255), "Ship flight controls active")
        chat.AddText(Color(200, 200, 200), "WASD = Move | Space/Ctrl = Up/Down | Shift = Boost")
        chat.AddText(Color(200, 200, 200), "Mouse = Look | R = Autopilot | T = Auto-Level | X = Brake")
    else
        -- Remove HUD display
        hook.Remove("HUDPaint", "ASC_SeatFlightHUD")
        
        chat.AddText(Color(255, 100, 100), "[ASC Flight] ", Color(255, 255, 255), "Ship flight controls deactivated")
    end
end

-- Draw flight control HUD
function ASC.SeatFlight.DrawHUD()
    if not ASC.SeatFlight.State.active then return end
    
    local shipCore = ASC.SeatFlight.State.shipCore
    if not IsValid(shipCore) then return end
    
    -- HUD position
    local x, y = 50, ScrH() - 200
    local w, h = 300, 150
    
    -- Background
    draw.RoundedBox(8, x, y, w, h, Color(0, 0, 0, 150))
    draw.RoundedBox(8, x, y, w, 25, Color(50, 100, 150, 200))
    
    -- Title
    draw.SimpleText("SHIP FLIGHT CONTROL", "DermaDefault", x + w/2, y + 12, Color(255, 255, 255), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    
    -- Flight status
    local flightActive = shipCore:GetNWBool("FlightActive", false)
    local flightMode = shipCore:GetNWString("FlightMode", "MANUAL")
    local flightSpeed = shipCore:GetNWFloat("FlightSpeed", 0)
    local autopilotActive = shipCore:GetNWBool("AutopilotActive", false)
    
    y = y + 35
    draw.SimpleText("Status: " .. (flightActive and "ACTIVE" or "INACTIVE"), "DermaDefault", x + 10, y, flightActive and Color(100, 255, 100) or Color(255, 100, 100))
    y = y + 20
    draw.SimpleText("Mode: " .. flightMode, "DermaDefault", x + 10, y, Color(255, 255, 255))
    y = y + 20
    draw.SimpleText("Speed: " .. math.Round(flightSpeed) .. " units/s", "DermaDefault", x + 10, y, Color(255, 255, 255))
    y = y + 20
    draw.SimpleText("Autopilot: " .. (autopilotActive and "ON" or "OFF"), "DermaDefault", x + 10, y, autopilotActive and Color(100, 255, 100) or Color(200, 200, 200))
    
    -- Input indicators
    local state = ASC.SeatFlight.State.inputState
    y = y + 25
    local inputText = ""
    if state.forward and state.forward > 0 then inputText = inputText .. "W " end
    if state.backward and state.backward > 0 then inputText = inputText .. "S " end
    if state.left and state.left > 0 then inputText = inputText .. "A " end
    if state.right and state.right > 0 then inputText = inputText .. "D " end
    if state.up and state.up > 0 then inputText = inputText .. "↑ " end
    if state.down and state.down > 0 then inputText = inputText .. "↓ " end
    if state.boost then inputText = inputText .. "BOOST " end
    
    if inputText ~= "" then
        draw.SimpleText("Input: " .. inputText, "DermaDefault", x + 10, y, Color(100, 255, 100))
    end
end

-- Network message handler
net.Receive("asc_seat_flight_control", function()
    local shipCore = net.ReadEntity()
    local takeControl = net.ReadBool()
    
    if takeControl then
        local keyBindings = net.ReadTable()
        local sensitivity = net.ReadTable()
        ASC.SeatFlight.Activate(shipCore, keyBindings, sensitivity)
    else
        ASC.SeatFlight.Deactivate()
    end
end)

-- Initialize when client loads
hook.Add("InitPostEntity", "ASC_SeatFlightInit", function()
    timer.Simple(1, function()
        ASC.SeatFlight.Initialize()
    end)
end)
