-- Advanced Space Combat - Enhanced Pulse Cannon Shared

ENT.Type = "anim"
ENT.Base = "asc_weapon_base"

ENT.PrintName = "ASC Enhanced Pulse Cannon"
ENT.Author = "Advanced Space Combat"
ENT.Contact = ""
ENT.Purpose = "High-frequency energy weapon with advanced targeting"
ENT.Instructions = "Use to toggle auto-fire mode. Effective against shields and energy systems."

ENT.Spawnable = true
ENT.AdminSpawnable = true
ENT.Category = "Advanced Space Combat - Weapons"

-- Enhanced weapon properties
ENT.WeaponName = "Enhanced Pulse Cannon"
ENT.WeaponType = "pulse_cannon"
ENT.WeaponModel = "models/props_c17/oildrum001_explosive.mdl"
ENT.WeaponMass = 150

-- Enhanced combat properties
ENT.Damage = 85
ENT.Range = 3000
ENT.FireRate = 2.5
ENT.EnergyConsumption = 20
ENT.ProjectileSpeed = 2500
ENT.ProjectileType = "pulse_bolt"
ENT.Accuracy = 0.95

-- Heat management
ENT.MaxHeat = 100
ENT.HeatPerShot = 12
ENT.CooldownRate = 30
ENT.OverheatThreshold = 85

-- Ammunition
ENT.MaxAmmo = -1
ENT.AmmoType = "energy"

-- Enhanced sounds
ENT.Sounds = {
    fire = "weapons/physcannon/energy_sing_loop4.wav",
    overheat = "ambient/energy/spark6.wav",
    cooldown = "ambient/energy/weld2.wav",
    charge = "ambient/energy/weld1.wav"
} -- Fallback model for pulse cannon

-- Network variables
function ENT:SetupDataTables()
    self:NetworkVar("Entity", 0, "ShipCore")
    self:NetworkVar("Bool", 0, "AutoFire")
    self:NetworkVar("Float", 0, "Energy")
    self:NetworkVar("Int", 0, "Ammo")
end
