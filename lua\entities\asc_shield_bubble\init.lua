--[[
    Advanced Space Combat - Shield Bubble Entity
    
    Visual shield bubble effect with collision detection
    Features:
    - Dynamic shield visualization
    - Damage absorption
    - Real-time strength updates
    - CAP integration
    - Advanced visual effects
]]

AddCSLuaFile("cl_init.lua")
AddCSLuaFile("shared.lua")
include("shared.lua")

function ENT:Initialize()
    -- Basic entity setup
    self:SetModel("models/effects/teleporttrail.mdl")
    self:SetMoveType(MOVETYPE_NONE)
    self:SetSolid(SOLID_NONE)
    self:SetCollisionGroup(COLLISION_GROUP_WORLD)
    self:SetRenderMode(RENDERMODE_TRANSALPHA)
    
    -- Shield properties
    self.ShieldRadius = 200
    self.ShieldStrength = 1000
    self.MaxShieldStrength = 1000
    self.ShieldColor = Color(100, 150, 255, 100)
    self.ShieldGenerator = nil
    self.Active = true
    
    -- Visual effects
    self.LastEffectTime = 0
    self.EffectRate = 0.1
    self.PulseIntensity = 0
    self.PulseDirection = 1
    
    -- Damage tracking
    self.LastDamageTime = 0
    self.DamageFlash = 0
    self.HitPositions = {}
    
    -- Performance
    self.LastUpdate = CurTime()
    self.UpdateRate = 0.05 -- 20 FPS
    
    -- Networking
    self:SetNWFloat("ShieldRadius", self.ShieldRadius)
    self:SetNWFloat("ShieldStrength", self.ShieldStrength)
    self:SetNWFloat("MaxShieldStrength", self.MaxShieldStrength)
    self:SetNWVector("ShieldColor", Vector(self.ShieldColor.r, self.ShieldColor.g, self.ShieldColor.b))
    self:SetNWFloat("ShieldAlpha", self.ShieldColor.a)
    self:SetNWBool("Active", true)
    
    print("[ASC Shield Bubble] Initialized with radius " .. self.ShieldRadius)
end

-- Set shield properties
function ENT:SetShieldRadius(radius)
    self.ShieldRadius = radius
    self:SetNWFloat("ShieldRadius", radius)
    
    -- Update collision bounds
    local bounds = Vector(radius, radius, radius)
    self:SetCollisionBounds(-bounds, bounds)
end

function ENT:SetShieldStrength(strength)
    self.ShieldStrength = strength
    self:SetNWFloat("ShieldStrength", strength)
    
    -- Update visual intensity based on strength
    local intensity = strength / self.MaxShieldStrength
    local alpha = math.Clamp(self.ShieldColor.a * intensity, 20, self.ShieldColor.a)
    self:SetNWFloat("ShieldAlpha", alpha)
end

function ENT:SetMaxShieldStrength(maxStrength)
    self.MaxShieldStrength = maxStrength
    self:SetNWFloat("MaxShieldStrength", maxStrength)
end

function ENT:SetShieldColor(color)
    self.ShieldColor = color
    self:SetNWVector("ShieldColor", Vector(color.r, color.g, color.b))
    self:SetNWFloat("ShieldAlpha", color.a)
end

function ENT:SetShieldGenerator(generator)
    self.ShieldGenerator = generator
    self:SetNWEntity("ShieldGenerator", generator)
end

-- Main think function
function ENT:Think()
    local currentTime = CurTime()
    
    -- Check if generator is still valid
    if not IsValid(self.ShieldGenerator) then
        self:Remove()
        return
    end
    
    -- Update position to match generator
    if IsValid(self.ShieldGenerator) then
        self:SetPos(self.ShieldGenerator:GetPos())
    end
    
    -- Update visual effects
    if currentTime - self.LastEffectTime > self.EffectRate then
        self:UpdateVisualEffects()
        self.LastEffectTime = currentTime
    end
    
    -- Update pulse animation
    self:UpdatePulseAnimation()
    
    -- Update damage flash
    if self.DamageFlash > 0 then
        self.DamageFlash = math.max(0, self.DamageFlash - FrameTime() * 5)
    end
    
    -- Clean up old hit positions
    for i = #self.HitPositions, 1, -1 do
        local hit = self.HitPositions[i]
        if currentTime - hit.time > 2.0 then
            table.remove(self.HitPositions, i)
        end
    end
    
    self:NextThink(currentTime + self.UpdateRate)
    return true
end

-- Update visual effects
function ENT:UpdateVisualEffects()
    if not self.Active or self.ShieldStrength <= 0 then return end
    
    -- Create ambient shield particles
    if math.random() < 0.3 then
        self:CreateAmbientParticles()
    end
    
    -- Update networking for smooth visual updates
    local strengthPercent = self.ShieldStrength / self.MaxShieldStrength
    local alpha = math.Clamp(self.ShieldColor.a * strengthPercent, 20, self.ShieldColor.a)
    self:SetNWFloat("ShieldAlpha", alpha)
end

-- Update pulse animation
function ENT:UpdatePulseAnimation()
    self.PulseIntensity = self.PulseIntensity + (self.PulseDirection * FrameTime() * 2)
    
    if self.PulseIntensity >= 1 then
        self.PulseIntensity = 1
        self.PulseDirection = -1
    elseif self.PulseIntensity <= 0 then
        self.PulseIntensity = 0
        self.PulseDirection = 1
    end
    
    self:SetNWFloat("PulseIntensity", self.PulseIntensity)
end

-- Create ambient particles
function ENT:CreateAmbientParticles()
    local effectData = EffectData()
    effectData:SetOrigin(self:GetPos() + VectorRand() * self.ShieldRadius * 0.8)
    effectData:SetMagnitude(0.5)
    effectData:SetScale(1)
    util.Effect("asc_shield_ambient", effectData)
end

-- Handle damage to shield
function ENT:OnDamage(damage, hitPos)
    if not self.Active or self.ShieldStrength <= 0 then return end
    
    self.LastDamageTime = CurTime()
    self.DamageFlash = 1.0
    
    -- Store hit position for visual effects
    table.insert(self.HitPositions, {
        pos = hitPos,
        time = CurTime(),
        damage = damage
    })
    
    -- Create impact effect
    self:CreateImpactEffect(hitPos, damage)
    
    -- Update networking
    self:SetNWFloat("DamageFlash", self.DamageFlash)
    self:SetNWFloat("LastDamageTime", self.LastDamageTime)
end

-- Create impact effect
function ENT:CreateImpactEffect(hitPos, damage)
    -- Main impact effect
    local effectData = EffectData()
    effectData:SetOrigin(hitPos)
    effectData:SetNormal((hitPos - self:GetPos()):GetNormalized())
    effectData:SetMagnitude(damage)
    effectData:SetScale(1.5)
    effectData:SetEntity(self)
    util.Effect("asc_shield_impact", effectData)
    
    -- Ripple effect
    local effectData2 = EffectData()
    effectData2:SetOrigin(self:GetPos())
    effectData2:SetStart(hitPos)
    effectData2:SetMagnitude(self.ShieldRadius)
    effectData2:SetScale(damage / 100)
    util.Effect("asc_shield_ripple", effectData2)
    
    -- Sound effect
    local pitch = math.Clamp(100 + (damage / 10), 80, 120)
    self:EmitSound("ambient/energy/spark" .. math.random(1, 6) .. ".wav", 70, pitch)
end

-- Activate shield
function ENT:Activate()
    self.Active = true
    self:SetNWBool("Active", true)
    
    -- Create activation effect
    local effectData = EffectData()
    effectData:SetOrigin(self:GetPos())
    effectData:SetMagnitude(self.ShieldRadius)
    effectData:SetScale(2)
    util.Effect("asc_shield_activate", effectData)
    
    self:EmitSound("ambient/energy/force_field_loop1.wav", 80, 100)
end

-- Deactivate shield
function ENT:Deactivate()
    self.Active = false
    self:SetNWBool("Active", false)
    
    -- Create deactivation effect
    local effectData = EffectData()
    effectData:SetOrigin(self:GetPos())
    effectData:SetMagnitude(self.ShieldRadius)
    effectData:SetScale(1)
    util.Effect("asc_shield_deactivate", effectData)
    
    self:EmitSound("ambient/energy/weld2.wav", 80, 80)
end

-- Check if position is within shield
function ENT:IsPositionProtected(pos)
    if not self.Active or self.ShieldStrength <= 0 then return false end
    
    local distance = self:GetPos():Distance(pos)
    return distance <= self.ShieldRadius
end

-- Get shield status
function ENT:GetShieldStatus()
    return {
        active = self.Active,
        strength = self.ShieldStrength,
        maxStrength = self.MaxShieldStrength,
        radius = self.ShieldRadius,
        strengthPercent = self.ShieldStrength / self.MaxShieldStrength,
        color = self.ShieldColor,
        generator = self.ShieldGenerator,
        lastDamageTime = self.LastDamageTime,
        hitPositions = self.HitPositions
    }
end

-- Cleanup
function ENT:OnRemove()
    -- Clean up any effects
    if IsValid(self.ShieldGenerator) then
        print("[ASC Shield Bubble] Shield bubble removed for generator " .. tostring(self.ShieldGenerator))
    end
end
