-- ASC Ship Core Entity - Client v2.2.0
-- Mandatory ship core for Advanced Space Combat with real-time features

include("shared.lua")

-- Enhanced UI variables with modern design and v3.0.0 features
local shipCoreUI = {
    visible = false,
    entity = nil,
    data = {},
    panelWidth = 1400,
    panelHeight = 1000,
    lastUpdate = 0,
    currentTab = "overview", -- overview, resources, systems, hull, combat, flight, admin
    animationTime = 0,
    fadeAlpha = 0,
    targetAlpha = 0,
    tabAnimations = {},
    scrollOffset = 0,
    maxScroll = 0,

    -- v3.0.0 enhancements
    realTimeMode = true,
    updateRate = 0.05, -- 20 FPS real-time updates
    lastRealTimeUpdate = 0,
    alertSystem = {},
    fleetData = {},
    adminData = {},
    stargateData = {},
    performanceData = {},
    notifications = {},
    lastNotificationTime = 0,
    hoverEffects = {},
    buttonStates = {},

    -- Modern theme with gradients and effects
    theme = {
        primary = Color(15, 25, 45, 250),
        secondary = Color(25, 40, 70, 230),
        tertiary = Color(35, 55, 90, 210),
        accent = Color(64, 156, 255),
        accentHover = Color(84, 176, 255),
        success = Color(76, 175, 80),
        warning = Color(255, 193, 7),
        error = Color(244, 67, 54),
        critical = Color(255, 87, 34),
        text = Color(255, 255, 255),
        textSecondary = Color(220, 220, 220),
        textMuted = Color(160, 160, 160),
        border = Color(100, 150, 255, 100),
        borderHover = Color(120, 170, 255, 150),
        background = Color(10, 15, 25, 200),
        cardBackground = Color(20, 30, 50, 180),
        progressBar = Color(76, 175, 80),
        progressBarBg = Color(50, 50, 50, 150)
    },

    -- Animation states
    animations = {
        tabSwitchTime = 0,
        tabSwitchDuration = 0.3,
        hoverAnimations = {},
        pulseTime = 0,
        glowIntensity = 0
    }
}

function ENT:Initialize()
    self.nextParticle = 0
    self.glowIntensity = 0
    self.pulseTime = 0
    self.lastEffectTime = 0
end

function ENT:Draw()
    self:DrawModel()

    -- Draw status text above core
    self:DrawStatusText()


end

function ENT:DrawStatusText()
    local pos = self:GetPos() + Vector(0, 0, 20)
    local ang = LocalPlayer():EyeAngles()
    ang:RotateAroundAxis(ang:Forward(), 90)
    ang:RotateAroundAxis(ang:Right(), 90)

    -- Check if ship name should be shown above core
    local config = HYPERDRIVE.EnhancedConfig and HYPERDRIVE.EnhancedConfig.ShipNaming or {}
    local showNameAboveCore = config.ShowNameAboveCore ~= false

    cam.Start3D2D(pos, ang, 0.1)
        -- Background (adjust height based on whether ship name is shown)
        local bgHeight = showNameAboveCore and 90 or 60
        local bgY = showNameAboveCore and -45 or -30
        draw.RoundedBox(8, -120, bgY, 240, bgHeight, Color(0, 0, 0, 150))

        local textY = bgY + 10

        -- Ship name (if enabled)
        if showNameAboveCore then
            local shipName = self:GetShipName() or (config.DefaultShipName or "Unnamed Ship")
            draw.SimpleText(shipName, "DermaDefaultBold", 0, textY, Color(100, 200, 255), TEXT_ALIGN_CENTER)
            textY = textY + 15
        end

        -- Core status
        local stateName = self:GetStateName()
        local stateColor = self:GetStateColor()
        draw.SimpleText("ASC SHIP CORE", "DermaDefaultBold", 0, textY, Color(255, 255, 255), TEXT_ALIGN_CENTER)
        textY = textY + 15
        draw.SimpleText(stateName, "DermaDefault", 0, textY, stateColor, TEXT_ALIGN_CENTER)
        textY = textY + 20

        -- Hull and shield status
        if self:GetHullSystemActive() then
            local hullColor = self:GetHullColor()
            draw.SimpleText("Hull: " .. self:GetHullIntegrity() .. "%", "DermaDefault", -50, textY, hullColor, TEXT_ALIGN_CENTER)
        end

        if self:GetShieldSystemActive() then
            local shieldColor = self:GetShieldColor()
            draw.SimpleText("Shield: " .. self:GetShieldStrength() .. "%", "DermaDefault", 50, textY, shieldColor, TEXT_ALIGN_CENTER)
        end
    cam.End3D2D()
end

-- Enhanced UI Functions with animations
function shipCoreUI:Open(entity, data)
    self.visible = true
    self.entity = entity
    self.data = data or {}
    self.targetAlpha = 255
    self.animationTime = CurTime()
    self.currentTab = "overview"

    -- Initialize tab animations
    self.tabAnimations = {}
    for _, tab in ipairs({"overview", "resources", "systems", "cap"}) do
        self.tabAnimations[tab] = {
            scale = 1.0,
            targetScale = 1.0,
            lastClick = 0
        }
    end

    gui.EnableScreenClicker(true)

    -- Play opening sound using sound system
    if HYPERDRIVE.Sounds and HYPERDRIVE.Sounds.UI then
        HYPERDRIVE.Sounds.UI.PlayOpen()
    else
        surface.PlaySound("buttons/button15.wav")
    end

    -- Add opening notification
    self:AddNotification("ASC Ship Core Interface Activated", "success")

    print("[ASC Ship Core UI] Enhanced interface opened")
end

function shipCoreUI:Close()
    self.targetAlpha = 0

    -- Animate close
    timer.Simple(0.3, function()
        if not self.visible then return end

        self.visible = false
        self.entity = nil
        self.data = {}
        self.notifications = {}
        gui.EnableScreenClicker(false)

        -- Send close command to server
        if IsValid(self.entity) then
            net.Start("asc_ship_core_command")
            net.WriteEntity(self.entity)
            net.WriteString("close_ui")
            net.WriteTable({})
            net.SendToServer()
        end
    end)

    -- Play closing sound using sound system
    if HYPERDRIVE.Sounds and HYPERDRIVE.Sounds.UI then
        HYPERDRIVE.Sounds.UI.PlayClose()
    else
        surface.PlaySound("buttons/button10.wav")
    end

    print("[ASC Ship Core UI] Enhanced interface closed")
end

function shipCoreUI:Update(data)
    self.data = data or {}
    self.lastUpdate = CurTime()

    -- Add update notification for critical changes
    -- Define states locally to avoid ENT context issues
    local States = {
        INACTIVE = 0,
        ACTIVE = 1,
        INVALID = 2,
        CRITICAL = 3,
        EMERGENCY = 4
    }

    if data.coreState == States.EMERGENCY and self.data.coreState ~= States.EMERGENCY then
        self:AddNotification("EMERGENCY: Core System Critical!", "error")
    elseif data.resourceEmergencyMode and not self.data.resourceEmergencyMode then
        self:AddNotification("WARNING: Resource Emergency Detected", "warning")
    end
end

-- Animation and notification system
function shipCoreUI:AddNotification(text, type)
    local notification = {
        text = text,
        type = type or "info",
        time = CurTime(),
        alpha = 255,
        y = 0
    }

    table.insert(self.notifications, notification)
    self.lastNotificationTime = CurTime()

    -- Limit notifications
    if #self.notifications > 5 then
        table.remove(self.notifications, 1)
    end

    -- Notification sounds removed per user request
end

-- Enhanced UI Drawing Function with modern design
function shipCoreUI:Draw()
    if not self.visible or not IsValid(self.entity) then return end

    -- Update animations
    self:UpdateAnimations()

    -- Update fade animation
    local timeDiff = CurTime() - self.animationTime
    if timeDiff < 0.3 then
        local progress = math.ease.InOutQuad(timeDiff / 0.3)
        self.fadeAlpha = Lerp(progress, 0, self.targetAlpha)
    else
        self.fadeAlpha = self.targetAlpha
    end

    if self.fadeAlpha <= 0 then return end

    local scrW, scrH = ScrW(), ScrH()
    local panelX = (scrW - self.panelWidth) / 2
    local panelY = (scrH - self.panelHeight) / 2

    -- Background blur effect
    self:DrawBlurBackground(panelX, panelY)

    -- Main panel background with gradient
    self:DrawGradientPanel(panelX, panelY, self.panelWidth, self.panelHeight)

    -- Panel border with glow effect
    self:DrawGlowBorder(panelX, panelY, self.panelWidth, self.panelHeight)

    -- Enhanced title bar
    self:DrawTitleBar(panelX, panelY)

    -- Modern tab system
    local tabY = panelY + 80
    self:DrawModernTabs(panelX, tabY)

    -- Content area with cards
    local contentY = tabY + 50
    local contentHeight = self.panelHeight - 130 - 50
    self:DrawContentArea(panelX + 20, contentY, self.panelWidth - 40, contentHeight)

    -- Status indicators
    self:DrawStatusIndicators(panelX, panelY)

    -- Real-time notifications
    self:DrawEnhancedNotifications()

    -- Performance overlay
    if self.data.showPerformance then
        self:DrawPerformanceOverlay(panelX, panelY + self.panelHeight - 100)
    end
end

-- Update animation states
function shipCoreUI:UpdateAnimations()
    local time = CurTime()

    -- Update pulse animation
    self.animations.pulseTime = time
    self.animations.glowIntensity = (math.sin(time * 2) + 1) * 0.5

    -- Update hover animations
    for id, anim in pairs(self.animations.hoverAnimations) do
        if anim.target then
            anim.current = Lerp(FrameTime() * 8, anim.current, anim.target)
        end
    end
end

-- Draw blur background
function shipCoreUI:DrawBlurBackground(x, y)
    local blurMat = Material("pp/blurscreen")
    surface.SetMaterial(blurMat)
    surface.SetDrawColor(255, 255, 255, 100)

    for i = 1, 3 do
        blurMat:SetFloat("$blur", i * 2)
        blurMat:Recompute()
        render.UpdateScreenEffectTexture()
        surface.DrawTexturedRect(0, 0, ScrW(), ScrH())
    end
end

-- Draw gradient panel
function shipCoreUI:DrawGradientPanel(x, y, w, h)
    -- Main background
    surface.SetDrawColor(self.theme.primary.r, self.theme.primary.g, self.theme.primary.b, self.fadeAlpha * 0.95)
    surface.DrawRect(x, y, w, h)

    -- Gradient overlay
    local gradientMat = Material("gui/gradient")
    surface.SetMaterial(gradientMat)
    surface.SetDrawColor(self.theme.secondary.r, self.theme.secondary.g, self.theme.secondary.b, self.fadeAlpha * 0.3)
    surface.DrawTexturedRect(x, y, w, h / 3)
end

-- Draw glow border
function shipCoreUI:DrawGlowBorder(x, y, w, h)
    local glowIntensity = self.animations.glowIntensity
    local borderColor = Color(
        self.theme.accent.r,
        self.theme.accent.g,
        self.theme.accent.b,
        self.fadeAlpha * (0.5 + glowIntensity * 0.5)
    )

    -- Outer glow
    for i = 1, 3 do
        surface.SetDrawColor(borderColor.r, borderColor.g, borderColor.b, borderColor.a / (i * 2))
        surface.DrawOutlinedRect(x - i, y - i, w + i * 2, h + i * 2, 1)
    end

    -- Main border
    surface.SetDrawColor(borderColor.r, borderColor.g, borderColor.b, borderColor.a)
    surface.DrawOutlinedRect(x, y, w, h, 2)
end

-- Draw enhanced title bar
function shipCoreUI:DrawTitleBar(x, y)
    local titleHeight = 80

    -- Title background with gradient
    surface.SetDrawColor(self.theme.secondary.r, self.theme.secondary.g, self.theme.secondary.b, self.fadeAlpha)
    surface.DrawRect(x, y, self.panelWidth, titleHeight)

    -- Title gradient overlay
    local gradientMat = Material("gui/gradient_down")
    surface.SetMaterial(gradientMat)
    surface.SetDrawColor(self.theme.accent.r, self.theme.accent.g, self.theme.accent.b, self.fadeAlpha * 0.2)
    surface.DrawTexturedRect(x, y, self.panelWidth, titleHeight)

    -- Ship core icon (if available)
    local iconSize = 32
    local iconX = x + 20
    local iconY = y + (titleHeight - iconSize) / 2

    -- Title text with shadow
    local titleText = "Advanced Space Combat - Ship Core Interface v3.0"
    draw.SimpleText(titleText, "DermaLarge", iconX + iconSize + 15 + 1, y + titleHeight / 2 + 1, Color(0, 0, 0, self.fadeAlpha * 0.5), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)
    draw.SimpleText(titleText, "DermaLarge", iconX + iconSize + 15, y + titleHeight / 2, Color(self.theme.text.r, self.theme.text.g, self.theme.text.b, self.fadeAlpha), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)

    -- Ship name
    local shipName = self.data.shipName or "Unknown Ship"
    draw.SimpleText("Ship: " .. shipName, "DermaDefault", iconX + iconSize + 15, y + titleHeight / 2 + 20, Color(self.theme.textSecondary.r, self.theme.textSecondary.g, self.theme.textSecondary.b, self.fadeAlpha), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)

    -- Enhanced close button
    self:DrawCloseButton(x + self.panelWidth - 50, y + 15)
end

-- Draw modern close button
function shipCoreUI:DrawCloseButton(x, y)
    local size = 50
    local mx, my = gui.MousePos()
    local isHover = mx >= x and mx <= x + size and my >= y and my <= y + size

    -- Hover animation
    if not self.animations.hoverAnimations.closeButton then
        self.animations.hoverAnimations.closeButton = {current = 0, target = 0}
    end

    local hoverAnim = self.animations.hoverAnimations.closeButton
    hoverAnim.target = isHover and 1 or 0
    hoverAnim.current = Lerp(FrameTime() * 10, hoverAnim.current, hoverAnim.target)

    -- Button background
    local bgColor = Color(
        Lerp(hoverAnim.current, self.theme.error.r * 0.3, self.theme.error.r),
        Lerp(hoverAnim.current, self.theme.error.g * 0.3, self.theme.error.g),
        Lerp(hoverAnim.current, self.theme.error.b * 0.3, self.theme.error.b),
        self.fadeAlpha * (0.6 + hoverAnim.current * 0.4)
    )

    surface.SetDrawColor(bgColor.r, bgColor.g, bgColor.b, bgColor.a)
    surface.DrawRect(x, y, size, size)

    -- Button border
    surface.SetDrawColor(self.theme.error.r, self.theme.error.g, self.theme.error.b, self.fadeAlpha * (0.5 + hoverAnim.current * 0.5))
    surface.DrawOutlinedRect(x, y, size, size, 1)

    -- X symbol
    local centerX, centerY = x + size / 2, y + size / 2
    local crossSize = 12
    surface.SetDrawColor(255, 255, 255, self.fadeAlpha)

    -- Draw X lines
    surface.DrawLine(centerX - crossSize, centerY - crossSize, centerX + crossSize, centerY + crossSize)
    surface.DrawLine(centerX + crossSize, centerY - crossSize, centerX - crossSize, centerY + crossSize)

    -- Handle click
    if isHover and input.IsMouseDown(MOUSE_LEFT) then
        self:Close()
    end
end

-- Draw modern tab system
function shipCoreUI:DrawModernTabs(x, y)
    local tabs = {
        {id = "overview", name = "Overview", icon = "📊"},
        {id = "resources", name = "Resources", icon = "⚡"},
        {id = "systems", name = "Systems", icon = "⚙️"},
        {id = "hull", name = "Hull", icon = "🛡️"},
        {id = "combat", name = "Combat", icon = "⚔️"},
        {id = "flight", name = "Flight", icon = "🚀"}
    }

    local tabWidth = (self.panelWidth - 40) / #tabs
    local tabHeight = 45
    local mx, my = gui.MousePos()

    for i, tab in ipairs(tabs) do
        local tabX = x + 20 + (i - 1) * tabWidth
        local isActive = tab.id == self.currentTab
        local isHover = mx >= tabX and mx <= tabX + tabWidth and my >= y and my <= y + tabHeight

        -- Tab hover animation
        if not self.animations.hoverAnimations["tab_" .. tab.id] then
            self.animations.hoverAnimations["tab_" .. tab.id] = {current = 0, target = 0}
        end

        local hoverAnim = self.animations.hoverAnimations["tab_" .. tab.id]
        hoverAnim.target = (isHover and not isActive) and 1 or 0
        hoverAnim.current = Lerp(FrameTime() * 8, hoverAnim.current, hoverAnim.target)

        -- Tab background
        local bgColor
        if isActive then
            bgColor = Color(self.theme.accent.r, self.theme.accent.g, self.theme.accent.b, self.fadeAlpha)
        else
            local hoverIntensity = hoverAnim.current
            bgColor = Color(
                Lerp(hoverIntensity, self.theme.tertiary.r, self.theme.secondary.r),
                Lerp(hoverIntensity, self.theme.tertiary.g, self.theme.secondary.g),
                Lerp(hoverIntensity, self.theme.tertiary.b, self.theme.secondary.b),
                self.fadeAlpha * (0.7 + hoverIntensity * 0.3)
            )
        end

        surface.SetDrawColor(bgColor.r, bgColor.g, bgColor.b, bgColor.a)
        surface.DrawRect(tabX, y, tabWidth, tabHeight)

        -- Tab border
        if isActive then
            surface.SetDrawColor(self.theme.accent.r, self.theme.accent.g, self.theme.accent.b, self.fadeAlpha)
            surface.DrawRect(tabX, y + tabHeight - 3, tabWidth, 3)
        end

        -- Tab text
        local textColor = isActive and self.theme.text or self.theme.textSecondary
        local textAlpha = self.fadeAlpha * (isActive and 1 or (0.8 + hoverAnim.current * 0.2))

        -- Icon
        draw.SimpleText(tab.icon, "DermaDefault", tabX + 15, y + tabHeight / 2, Color(textColor.r, textColor.g, textColor.b, textAlpha), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)

        -- Tab name
        draw.SimpleText(tab.name, "DermaDefault", tabX + 35, y + tabHeight / 2, Color(textColor.r, textColor.g, textColor.b, textAlpha), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)

        -- Handle tab click
        if isHover and input.IsMouseDown(MOUSE_LEFT) and not self.buttonStates.tabClicked then
            self.currentTab = tab.id
            self.animations.tabSwitchTime = CurTime()
            self.buttonStates.tabClicked = true
            surface.PlaySound("buttons/button15.wav")
        end
    end

    -- Reset click state
    if not input.IsMouseDown(MOUSE_LEFT) then
        self.buttonStates.tabClicked = false
    end
end

-- Draw content area with cards
function shipCoreUI:DrawContentArea(x, y, w, h)
    -- Content background
    surface.SetDrawColor(self.theme.cardBackground.r, self.theme.cardBackground.g, self.theme.cardBackground.b, self.fadeAlpha * 0.8)
    surface.DrawRect(x, y, w, h)

    -- Content border
    surface.SetDrawColor(self.theme.border.r, self.theme.border.g, self.theme.border.b, self.fadeAlpha * 0.5)
    surface.DrawOutlinedRect(x, y, w, h, 1)

    -- Draw content based on current tab with smooth transitions
    local contentAlpha = 1
    if CurTime() - self.animations.tabSwitchTime < self.animations.tabSwitchDuration then
        local progress = (CurTime() - self.animations.tabSwitchTime) / self.animations.tabSwitchDuration
        contentAlpha = math.ease.InOutQuad(progress)
    end

    -- Set content alpha for smooth transitions
    local oldFadeAlpha = self.fadeAlpha
    self.fadeAlpha = self.fadeAlpha * contentAlpha

    if self.currentTab == "overview" then
        self:DrawEnhancedOverviewTab(x + 20, y + 20, w - 40, h - 40)
    elseif self.currentTab == "resources" then
        self:DrawEnhancedResourcesTab(x + 20, y + 20, w - 40, h - 40)
    elseif self.currentTab == "systems" then
        self:DrawEnhancedSystemsTab(x + 20, y + 20, w - 40, h - 40)
    elseif self.currentTab == "hull" then
        self:DrawHullTab(x + 20, y + 20, w - 40, h - 40)
    elseif self.currentTab == "combat" then
        self:DrawCombatTab(x + 20, y + 20, w - 40, h - 40)
    elseif self.currentTab == "flight" then
        self:DrawFlightTab(x + 20, y + 20, w - 40, h - 40)
    end

    -- Restore original alpha
    self.fadeAlpha = oldFadeAlpha
end

-- Draw status indicators
function shipCoreUI:DrawStatusIndicators(x, y)
    local indicatorSize = 12
    local indicatorSpacing = 20
    local startX = x + self.panelWidth - 200
    local startY = y + 15

    local indicators = {
        {name = "Power", status = self.data.powerStatus or "unknown", color = self.theme.success},
        {name = "Hull", status = self.data.hullStatus or "unknown", color = self.theme.warning},
        {name = "Shields", status = self.data.shieldStatus or "unknown", color = self.theme.accent},
        {name = "Systems", status = self.data.systemStatus or "unknown", color = self.theme.success}
    }

    for i, indicator in ipairs(indicators) do
        local indX = startX + (i - 1) * (indicatorSize + indicatorSpacing)

        -- Indicator background
        surface.SetDrawColor(50, 50, 50, self.fadeAlpha)
        surface.DrawRect(indX, startY, indicatorSize, indicatorSize)

        -- Indicator status
        local statusColor = indicator.color
        if indicator.status == "critical" then
            statusColor = self.theme.error
        elseif indicator.status == "warning" then
            statusColor = self.theme.warning
        elseif indicator.status == "offline" then
            statusColor = self.theme.textMuted
        end

        surface.SetDrawColor(statusColor.r, statusColor.g, statusColor.b, self.fadeAlpha)
        surface.DrawRect(indX + 2, startY + 2, indicatorSize - 4, indicatorSize - 4)

        -- Indicator label
        draw.SimpleText(indicator.name, "DermaDefault", indX + indicatorSize / 2, startY + indicatorSize + 5, Color(self.theme.textMuted.r, self.theme.textMuted.g, self.theme.textMuted.b, self.fadeAlpha), TEXT_ALIGN_CENTER, TEXT_ALIGN_TOP)
    end
end

-- Enhanced Overview Tab
function shipCoreUI:DrawEnhancedOverviewTab(x, y, w, h)
    local data = self.data
    if not data then return end

    local cardHeight = 180
    local cardSpacing = 20
    local cardsPerRow = 2
    local cardWidth = (w - cardSpacing) / cardsPerRow

    -- Ship Status Card
    self:DrawInfoCard(x, y, cardWidth, cardHeight, "Ship Status", {
        {"Ship Name", data.shipName or "Unknown Ship"},
        {"Ship Type", data.shipType or "Unknown"},
        {"Core State", data.coreStateName or "Unknown"},
        {"Entities", tostring(data.entityCount or 0)},
        {"Players", tostring(data.playerCount or 0)}
    })

    -- System Health Card
    self:DrawInfoCard(x + cardWidth + cardSpacing, y, cardWidth, cardHeight, "System Health", {
        {"Hull Integrity", (data.hullIntegrity or 100) .. "%", self:GetHealthColor(data.hullIntegrity or 100)},
        {"Shield Strength", (data.shieldStrength or 0) .. "%", self:GetHealthColor(data.shieldStrength or 0)},
        {"Power Level", (data.powerLevel or 100) .. "%", self:GetHealthColor(data.powerLevel or 100)},
        {"Temperature", (data.temperature or 20) .. "°C", data.temperature and (data.temperature > 80 and self.theme.error or self.theme.success) or self.theme.textSecondary}
    })

    -- Performance Card
    local perfY = y + cardHeight + cardSpacing
    self:DrawInfoCard(x, perfY, cardWidth, cardHeight, "Performance", {
        {"CPU Usage", (data.cpuUsage or 0) .. "%", self:GetPerformanceColor(data.cpuUsage or 0)},
        {"Memory Usage", (data.memoryUsage or 0) .. "%", self:GetPerformanceColor(data.memoryUsage or 0)},
        {"Network Load", (data.networkLoad or 0) .. "%", self:GetPerformanceColor(data.networkLoad or 0)},
        {"Update Rate", (data.updateRate or 20) .. " FPS", data.updateRate and (data.updateRate < 15 and self.theme.warning or self.theme.success) or self.theme.textSecondary}
    })

    -- Alerts Card
    self:DrawAlertsCard(x + cardWidth + cardSpacing, perfY, cardWidth, cardHeight)
end

-- Enhanced Resources Tab
function shipCoreUI:DrawEnhancedResourcesTab(x, y, w, h)
    local data = self.data
    if not data or not data.resources then
        draw.SimpleText("No resource data available", "DermaDefaultBold", x + w/2, y + h/2, Color(self.theme.textMuted.r, self.theme.textMuted.g, self.theme.textMuted.b, self.fadeAlpha), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        return
    end

    local resourceHeight = 80
    local resourceSpacing = 15
    local currentY = y

    draw.SimpleText("Resource Management", "DermaLarge", x, currentY, Color(self.theme.text.r, self.theme.text.g, self.theme.text.b, self.fadeAlpha))
    currentY = currentY + 40

    -- Sort resources by priority
    local sortedResources = {}
    for resourceType, resource in pairs(data.resources) do
        table.insert(sortedResources, {type = resourceType, data = resource})
    end
    table.sort(sortedResources, function(a, b) return (a.data.priority or 0) < (b.data.priority or 0) end)

    for _, resourceInfo in ipairs(sortedResources) do
        if currentY + resourceHeight > y + h then break end

        local resourceType = resourceInfo.type
        local resource = resourceInfo.data

        self:DrawResourceBar(x, currentY, w, resourceHeight - 5, resourceType, resource)
        currentY = currentY + resourceHeight + resourceSpacing
    end
end

-- Enhanced Systems Tab
function shipCoreUI:DrawEnhancedSystemsTab(x, y, w, h)
    local data = self.data
    if not data then return end

    local cardHeight = 200
    local cardSpacing = 20
    local cardWidth = (w - cardSpacing) / 2

    -- Power Distribution Card
    self:DrawPowerDistributionCard(x, y, cardWidth, cardHeight)

    -- Subsystem Health Card
    self:DrawSubsystemHealthCard(x + cardWidth + cardSpacing, y, cardWidth, cardHeight)

    -- System Controls Card
    local controlsY = y + cardHeight + cardSpacing
    self:DrawSystemControlsCard(x, controlsY, w, 150)
end

-- Hull Tab
function shipCoreUI:DrawHullTab(x, y, w, h)
    local data = self.data
    if not data then return end

    draw.SimpleText("Hull Damage System", "DermaLarge", x, y, Color(self.theme.text.r, self.theme.text.g, self.theme.text.b, self.fadeAlpha))

    local cardHeight = 180
    local cardSpacing = 20
    local cardWidth = (w - cardSpacing) / 2
    local startY = y + 40

    -- Hull Status Card
    self:DrawHullStatusCard(x, startY, cardWidth, cardHeight)

    -- Hull Sections Card
    self:DrawHullSectionsCard(x + cardWidth + cardSpacing, startY, cardWidth, cardHeight)

    -- Hull History Card
    local historyY = startY + cardHeight + cardSpacing
    self:DrawHullHistoryCard(x, historyY, w, 150)
end

-- Combat Tab
function shipCoreUI:DrawCombatTab(x, y, w, h)
    local data = self.data
    if not data then return end

    draw.SimpleText("Combat Systems", "DermaLarge", x, y, Color(self.theme.text.r, self.theme.text.g, self.theme.text.b, self.fadeAlpha))

    local cardHeight = 180
    local cardSpacing = 20
    local cardWidth = (w - cardSpacing) / 2
    local startY = y + 40

    -- Combat Status Card
    self:DrawCombatStatusCard(x, startY, cardWidth, cardHeight)

    -- Targeting Card
    self:DrawTargetingCard(x + cardWidth + cardSpacing, startY, cardWidth, cardHeight)

    -- Weapons Status Card
    local weaponsY = startY + cardHeight + cardSpacing
    self:DrawWeaponsStatusCard(x, weaponsY, w, 150)
end

-- Flight Tab
function shipCoreUI:DrawFlightTab(x, y, w, h)
    local data = self.data
    if not data then return end

    draw.SimpleText("Flight Control Systems", "DermaLarge", x, y, Color(self.theme.text.r, self.theme.text.g, self.theme.text.b, self.fadeAlpha))

    local cardHeight = 180
    local cardSpacing = 20
    local cardWidth = (w - cardSpacing) / 2
    local startY = y + 40

    -- Flight Status Card
    self:DrawFlightStatusCard(x, startY, cardWidth, cardHeight)

    -- Cloaking Card
    self:DrawCloakingCard(x + cardWidth + cardSpacing, startY, cardWidth, cardHeight)

    -- Navigation Card
    local navY = startY + cardHeight + cardSpacing
    self:DrawNavigationCard(x, navY, w, 150)
end

-- Helper Functions for UI Elements

-- Draw info card
function shipCoreUI:DrawInfoCard(x, y, w, h, title, items)
    -- Card background
    surface.SetDrawColor(self.theme.cardBackground.r, self.theme.cardBackground.g, self.theme.cardBackground.b, self.fadeAlpha * 0.9)
    surface.DrawRect(x, y, w, h)

    -- Card border
    surface.SetDrawColor(self.theme.border.r, self.theme.border.g, self.theme.border.b, self.fadeAlpha * 0.6)
    surface.DrawOutlinedRect(x, y, w, h, 1)

    -- Card title
    surface.SetDrawColor(self.theme.secondary.r, self.theme.secondary.g, self.theme.secondary.b, self.fadeAlpha * 0.8)
    surface.DrawRect(x, y, w, 30)
    draw.SimpleText(title, "DermaDefaultBold", x + 10, y + 15, Color(self.theme.text.r, self.theme.text.g, self.theme.text.b, self.fadeAlpha), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)

    -- Card content
    local contentY = y + 40
    local lineHeight = 20

    for i, item in ipairs(items) do
        if contentY + lineHeight > y + h then break end

        local label = item[1]
        local value = item[2]
        local color = item[3] or self.theme.textSecondary

        draw.SimpleText(label .. ":", "DermaDefault", x + 10, contentY, Color(self.theme.textSecondary.r, self.theme.textSecondary.g, self.theme.textSecondary.b, self.fadeAlpha), TEXT_ALIGN_LEFT, TEXT_ALIGN_TOP)
        draw.SimpleText(tostring(value), "DermaDefault", x + w - 10, contentY, Color(color.r, color.g, color.b, self.fadeAlpha), TEXT_ALIGN_RIGHT, TEXT_ALIGN_TOP)

        contentY = contentY + lineHeight
    end
end

-- Draw resource bar
function shipCoreUI:DrawResourceBar(x, y, w, h, resourceType, resource)
    -- Background
    surface.SetDrawColor(self.theme.cardBackground.r, self.theme.cardBackground.g, self.theme.cardBackground.b, self.fadeAlpha * 0.9)
    surface.DrawRect(x, y, w, h)

    -- Border
    surface.SetDrawColor(self.theme.border.r, self.theme.border.g, self.theme.border.b, self.fadeAlpha * 0.6)
    surface.DrawOutlinedRect(x, y, w, h, 1)

    -- Resource name
    local displayName = string.upper(string.sub(resourceType, 1, 1)) .. string.sub(resourceType, 2)
    draw.SimpleText(displayName, "DermaDefaultBold", x + 10, y + 10, Color(self.theme.text.r, self.theme.text.g, self.theme.text.b, self.fadeAlpha))

    -- Progress bar
    local barX = x + 10
    local barY = y + 35
    local barW = w - 20
    local barH = 20

    -- Bar background
    surface.SetDrawColor(self.theme.progressBarBg.r, self.theme.progressBarBg.g, self.theme.progressBarBg.b, self.fadeAlpha)
    surface.DrawRect(barX, barY, barW, barH)

    -- Bar fill
    local fillWidth = (resource.percentage / 100) * barW
    local barColor = self:GetHealthColor(resource.percentage)
    surface.SetDrawColor(barColor.r, barColor.g, barColor.b, self.fadeAlpha)
    surface.DrawRect(barX, barY, fillWidth, barH)

    -- Bar text
    local barText = math.floor(resource.amount) .. "/" .. resource.capacity .. " (" .. math.floor(resource.percentage) .. "%)"
    draw.SimpleText(barText, "DermaDefault", barX + barW / 2, barY + barH / 2, Color(255, 255, 255, self.fadeAlpha), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)

    -- Regeneration rate
    if resource.regenRate and resource.regenRate > 0 then
        draw.SimpleText("+" .. resource.regenRate .. "/s", "DermaDefault", x + w - 10, y + 60, Color(self.theme.success.r, self.theme.success.g, self.theme.success.b, self.fadeAlpha), TEXT_ALIGN_RIGHT)
    end
end

-- Get health color based on percentage
function shipCoreUI:GetHealthColor(percentage)
    if percentage >= 75 then
        return self.theme.success
    elseif percentage >= 50 then
        return self.theme.warning
    elseif percentage >= 25 then
        return Color(255, 150, 50) -- Orange
    else
        return self.theme.error
    end
end

-- Get performance color based on usage
function shipCoreUI:GetPerformanceColor(usage)
    if usage <= 50 then
        return self.theme.success
    elseif usage <= 75 then
        return self.theme.warning
    else
        return self.theme.error
    end
end

-- Draw alerts card
function shipCoreUI:DrawAlertsCard(x, y, w, h)
    self:DrawInfoCard(x, y, w, h, "System Alerts", {
        {"Active Alerts", tostring(#(self.data.alerts or {}))},
        {"Last Alert", self.data.lastAlert or "None"},
        {"Alert Level", self.data.alertLevel or "Normal"},
        {"Auto-Repair", self.data.autoRepair and "Active" or "Inactive"}
    })
end

-- Draw enhanced notifications
function shipCoreUI:DrawEnhancedNotifications()
    if #self.notifications == 0 then return end

    local notifWidth = 400
    local notifHeight = 50
    local notifSpacing = 10
    local startX = ScrW() - notifWidth - 20
    local startY = 100

    for i, notif in ipairs(self.notifications) do
        local notifY = startY + (i - 1) * (notifHeight + notifSpacing)
        local age = CurTime() - notif.time
        local alpha = math.max(0, 255 - (age * 50)) -- Fade out over 5 seconds

        if alpha > 0 then
            -- Notification background
            local bgColor = self.theme.cardBackground
            if notif.type == "error" then
                bgColor = self.theme.error
            elseif notif.type == "warning" then
                bgColor = self.theme.warning
            elseif notif.type == "success" then
                bgColor = self.theme.success
            end

            surface.SetDrawColor(bgColor.r, bgColor.g, bgColor.b, alpha * 0.9)
            surface.DrawRect(startX, notifY, notifWidth, notifHeight)

            -- Notification border
            surface.SetDrawColor(bgColor.r, bgColor.g, bgColor.b, alpha)
            surface.DrawOutlinedRect(startX, notifY, notifWidth, notifHeight, 2)

            -- Notification text
            draw.SimpleText(notif.text, "DermaDefault", startX + 10, notifY + notifHeight / 2, Color(255, 255, 255, alpha), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)
        else
            -- Remove expired notifications
            table.remove(self.notifications, i)
            break
        end
    end
end

function shipCoreUI:DrawOverviewTab(x, y, w, h)
    local data = self.data
    if not data then return end

    local lineHeight = 20
    local currentY = y
    local columnWidth = w / 2

    -- Left Column - Ship Core Status
    draw.SimpleText("Ship Core Status:", "DermaDefaultBold", x, currentY, Color(self.theme.text.r, self.theme.text.g, self.theme.text.b, self.fadeAlpha))
    currentY = currentY + lineHeight

    draw.SimpleText("State: " .. (data.coreStateName or "Unknown"), "DermaDefault", x + 10, currentY, Color(self.theme.textSecondary.r, self.theme.textSecondary.g, self.theme.textSecondary.b, self.fadeAlpha))
    currentY = currentY + lineHeight

    draw.SimpleText("Ship: " .. (data.shipName or "Unnamed Ship"), "DermaDefault", x + 10, currentY, Color(self.theme.textSecondary.r, self.theme.textSecondary.g, self.theme.textSecondary.b, self.fadeAlpha))
    currentY = currentY + lineHeight

    draw.SimpleText("Type: " .. (data.shipType or "Unknown"), "DermaDefault", x + 10, currentY, Color(self.theme.textSecondary.r, self.theme.textSecondary.g, self.theme.textSecondary.b, self.fadeAlpha))
    currentY = currentY + lineHeight * 1.5

    -- Power Management
    if data.powerManagement then
        local pm = data.powerManagement
        draw.SimpleText("Power Management:", "DermaDefaultBold", x, currentY, Color(self.theme.text.r, self.theme.text.g, self.theme.text.b, self.fadeAlpha))
        currentY = currentY + lineHeight

        local powerPercent = (pm.availablePower / pm.totalPower) * 100
        local powerColor = powerPercent > 75 and self.theme.success or (powerPercent > 25 and self.theme.warning or self.theme.error)
        draw.SimpleText("Power: " .. math.floor(pm.availablePower) .. "/" .. pm.totalPower .. " (" .. math.floor(powerPercent) .. "%)", "DermaDefault", x + 10, currentY, Color(powerColor.r, powerColor.g, powerColor.b, self.fadeAlpha))
        currentY = currentY + lineHeight

        local efficiencyColor = pm.powerEfficiency > 0.8 and self.theme.success or (pm.powerEfficiency > 0.5 and self.theme.warning or self.theme.error)
        draw.SimpleText("Efficiency: " .. math.floor(pm.powerEfficiency * 100) .. "%", "DermaDefault", x + 10, currentY, Color(efficiencyColor.r, efficiencyColor.g, efficiencyColor.b, self.fadeAlpha))
        currentY = currentY + lineHeight

        if pm.emergencyMode then
            draw.SimpleText("EMERGENCY MODE ACTIVE", "DermaDefault", x + 10, currentY, Color(self.theme.error.r, self.theme.error.g, self.theme.error.b, self.fadeAlpha))
            currentY = currentY + lineHeight
        end
    end

    -- Right Column - System Status
    local rightX = x + columnWidth
    local rightY = y

    draw.SimpleText("System Status:", "DermaDefaultBold", rightX, rightY, Color(self.theme.text.r, self.theme.text.g, self.theme.text.b, self.fadeAlpha))
    rightY = rightY + lineHeight

    local hullColor = data.hullIntegrity and (data.hullIntegrity > 75 and self.theme.success or (data.hullIntegrity > 25 and self.theme.warning or self.theme.error)) or self.theme.textMuted
    draw.SimpleText("Hull: " .. (data.hullIntegrity or 0) .. "%", "DermaDefault", rightX + 10, rightY, Color(hullColor.r, hullColor.g, hullColor.b, self.fadeAlpha))
    rightY = rightY + lineHeight

    local shieldColor = data.shieldStrength and (data.shieldStrength > 50 and self.theme.success or (data.shieldStrength > 0 and self.theme.warning or self.theme.error)) or self.theme.textMuted
    draw.SimpleText("Shields: " .. (data.shieldStrength or 0) .. "%", "DermaDefault", rightX + 10, rightY, Color(shieldColor.r, shieldColor.g, shieldColor.b, self.fadeAlpha))
    rightY = rightY + lineHeight * 1.5

    -- Thermal Management
    if data.thermalManagement then
        local tm = data.thermalManagement
        draw.SimpleText("Thermal Status:", "DermaDefaultBold", rightX, rightY, Color(self.theme.text.r, self.theme.text.g, self.theme.text.b, self.fadeAlpha))
        rightY = rightY + lineHeight

        local tempColor = tm.coreTemperature < tm.maxTemperature and self.theme.success or self.theme.error
        draw.SimpleText("Core Temp: " .. math.floor(tm.coreTemperature) .. "°C", "DermaDefault", rightX + 10, rightY, Color(tempColor.r, tempColor.g, tempColor.b, self.fadeAlpha))
        rightY = rightY + lineHeight

        if tm.overheating then
            draw.SimpleText("OVERHEATING!", "DermaDefault", rightX + 10, rightY, Color(self.theme.error.r, self.theme.error.g, self.theme.error.b, self.fadeAlpha))
            rightY = rightY + lineHeight
        end
    end

    -- Crew Efficiency
    if data.crewEfficiency then
        local ce = data.crewEfficiency
        draw.SimpleText("Crew Status:", "DermaDefaultBold", rightX, rightY, Color(self.theme.text.r, self.theme.text.g, self.theme.text.b, self.fadeAlpha))
        rightY = rightY + lineHeight

        draw.SimpleText("Crew: " .. ce.totalCrew .. " aboard", "DermaDefault", rightX + 10, rightY, Color(self.theme.textSecondary.r, self.theme.textSecondary.g, self.theme.textSecondary.b, self.fadeAlpha))
        rightY = rightY + lineHeight

        local effColor = ce.overallEfficiency > 0.8 and self.theme.success or (ce.overallEfficiency > 0.5 and self.theme.warning or self.theme.error)
        draw.SimpleText("Efficiency: " .. math.floor(ce.overallEfficiency * 100) .. "%", "DermaDefault", rightX + 10, rightY, Color(effColor.r, effColor.g, effColor.b, self.fadeAlpha))
    end
end

function shipCoreUI:DrawResourcesTab(x, y, w, h)
    local data = self.data
    if not data then return end

    local lineHeight = 25
    local currentY = y
    local barHeight = 15
    local barWidth = w - 100

    draw.SimpleText("Resource Management", "DermaDefaultBold", x, currentY, Color(self.theme.text.r, self.theme.text.g, self.theme.text.b, self.fadeAlpha))
    currentY = currentY + lineHeight * 1.5

    if data.resources then
        -- Sort resources by priority
        local sortedResources = {}
        for resourceType, resource in pairs(data.resources) do
            table.insert(sortedResources, {type = resourceType, data = resource})
        end
        table.sort(sortedResources, function(a, b) return a.data.priority < b.data.priority end)

        for _, resourceInfo in ipairs(sortedResources) do
            local resourceType = resourceInfo.type
            local resource = resourceInfo.data

            -- Resource name
            local displayName = string.upper(string.sub(resourceType, 1, 1)) .. string.sub(resourceType, 2)
            draw.SimpleText(displayName .. ":", "DermaDefault", x, currentY, Color(self.theme.text.r, self.theme.text.g, self.theme.text.b, self.fadeAlpha))

            -- Resource bar background
            surface.SetDrawColor(50, 50, 50, self.fadeAlpha)
            surface.DrawRect(x + 80, currentY, barWidth, barHeight)

            -- Resource bar fill
            local fillWidth = (resource.percentage / 100) * barWidth
            local barColor = resource.percentage > 75 and self.theme.success or (resource.percentage > 25 and self.theme.warning or self.theme.error)
            if resource.critical and resource.percentage < 25 then
                barColor = self.theme.error
            end

            surface.SetDrawColor(barColor.r, barColor.g, barColor.b, self.fadeAlpha)
            surface.DrawRect(x + 80, currentY, fillWidth, barHeight)

            -- Resource text
            local resourceText = math.floor(resource.amount) .. "/" .. resource.capacity .. " (" .. math.floor(resource.percentage) .. "%)"
            draw.SimpleText(resourceText, "DermaDefault", x + 80 + barWidth + 10, currentY + 2, Color(self.theme.textSecondary.r, self.theme.textSecondary.g, self.theme.textSecondary.b, self.fadeAlpha))

            -- Regeneration rate
            if resource.regenRate > 0 then
                draw.SimpleText("+" .. resource.regenRate .. "/s", "DermaDefault", x + 80 + barWidth + 150, currentY + 2, Color(self.theme.success.r, self.theme.success.g, self.theme.success.b, self.fadeAlpha))
            end

            currentY = currentY + lineHeight
        end
    else
        draw.SimpleText("No resource data available", "DermaDefault", x, currentY, Color(self.theme.textMuted.r, self.theme.textMuted.g, self.theme.textMuted.b, self.fadeAlpha))
    end
end

function shipCoreUI:DrawSystemsTab(x, y, w, h)
    local data = self.data
    if not data then return end

    local lineHeight = 25
    local currentY = y
    local columnWidth = w / 2

    draw.SimpleText("System Configuration", "DermaDefaultBold", x, currentY, Color(self.theme.text.r, self.theme.text.g, self.theme.text.b, self.fadeAlpha))
    currentY = currentY + lineHeight * 1.5

    -- Left Column - Power Distribution
    if data.powerManagement and data.powerManagement.powerDistribution then
        draw.SimpleText("Power Distribution:", "DermaDefaultBold", x, currentY, Color(self.theme.text.r, self.theme.text.g, self.theme.text.b, self.fadeAlpha))
        local powerY = currentY + lineHeight

        for system, powerData in pairs(data.powerManagement.powerDistribution) do
            if powerData.active then
                local displayName = string.upper(string.sub(system, 1, 1)) .. string.sub(system, 2)
                local powerPercent = (powerData.allocated / data.powerManagement.totalPower) * 100

                draw.SimpleText(displayName .. ": " .. powerData.allocated .. "W (" .. math.floor(powerPercent) .. "%)", "DermaDefault", x + 10, powerY, Color(self.theme.textSecondary.r, self.theme.textSecondary.g, self.theme.textSecondary.b, self.fadeAlpha))
                powerY = powerY + lineHeight * 0.8
            end
        end
    end

    -- Right Column - Subsystem Health
    local rightX = x + columnWidth
    local rightY = currentY

    if data.subsystems then
        draw.SimpleText("Subsystem Health:", "DermaDefaultBold", rightX, rightY, Color(self.theme.text.r, self.theme.text.g, self.theme.text.b, self.fadeAlpha))
        rightY = rightY + lineHeight

        for systemName, subsystem in pairs(data.subsystems) do
            local displayName = string.upper(string.sub(systemName, 1, 1)) .. string.sub(systemName, 2)
            local healthColor = subsystem.health > 75 and self.theme.success or (subsystem.health > 25 and self.theme.warning or self.theme.error)

            local statusText = displayName .. ": " .. math.floor(subsystem.health) .. "%"
            if subsystem.critical and subsystem.health < 50 then
                statusText = statusText .. " [CRITICAL]"
            end

            draw.SimpleText(statusText, "DermaDefault", rightX + 10, rightY, Color(healthColor.r, healthColor.g, healthColor.b, self.fadeAlpha))
            rightY = rightY + lineHeight * 0.8
        end

        if data.autoRepair then
            rightY = rightY + lineHeight * 0.5
            draw.SimpleText("Auto-Repair: ACTIVE", "DermaDefault", rightX + 10, rightY, Color(self.theme.success.r, self.theme.success.g, self.theme.success.b, self.fadeAlpha))
        end
    end
end



function shipCoreUI:DrawNotifications()
    if not self.notifications or #self.notifications == 0 then return end

    local x = ScrW() - 320
    local y = 100

    for i, notification in ipairs(self.notifications) do
        local age = CurTime() - notification.time
        local alpha = math.max(0, 255 - (age * 50))

        if alpha > 0 then
            local color = self.theme.text
            if notification.type == "error" then
                color = self.theme.error
            elseif notification.type == "warning" then
                color = self.theme.warning
            elseif notification.type == "success" then
                color = self.theme.success
            end

            surface.SetDrawColor(0, 0, 0, alpha * 0.8)
            surface.DrawRect(x, y + (i - 1) * 30, 300, 25)

            draw.SimpleText(notification.text, "DermaDefault", x + 10, y + (i - 1) * 30 + 12, Color(color.r, color.g, color.b, alpha), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)
        end
    end
end

-- HUD Paint hook for UI rendering
hook.Add("HUDPaint", "ASC_ShipCore_UI", function()
    shipCoreUI:Draw()
end)



-- Network message handlers for UI system
net.Receive("asc_ship_core_open_ui", function()
    local core = net.ReadEntity()
    local data = net.ReadTable()

    if IsValid(core) then
        shipCoreUI:Open(core, data)
    end
end)

net.Receive("asc_ship_core_close_ui", function()
    shipCoreUI:Close()
end)

net.Receive("asc_ship_core_update_ui", function()
    local data = net.ReadTable()
    shipCoreUI:Update(data)
end)




