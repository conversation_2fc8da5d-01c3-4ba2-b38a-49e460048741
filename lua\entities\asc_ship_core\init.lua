-- ASC Ship Core Entity - Server v5.0.0 - Enhanced Stargate Hyperspace Edition
-- ENHANCED STARGATE HYPERSPACE UPDATE v5.0.0 - 4-STAGE TRAVEL SYSTEM INTEGRATION
-- Mandatory ship core for Advanced Space Combat with enhanced Stargate hyperspace features
-- Advanced ship core with ARIA-4 AI integration, 4-stage hyperspace support, and complete unification

print("[ASC Ship Core] ENHANCED STARGATE HYPERSPACE UPDATE v5.0.0 - ASC Ship Core Entity being updated")
print("[ASC Ship Core] Enhanced ASC ship core v5.0.0 with 4-stage Stargate hyperspace and ARIA-4 AI initializing...")

AddCSLuaFile("cl_init.lua")
AddCSLuaFile("shared.lua")
include("shared.lua")

-- Enhanced safe function call wrapper to prevent errors from missing functions
local function SafeCall(func, ...)
    if not func then return false, "Function is nil" end
    if type(func) ~= "function" then return false, "Not a function: " .. type(func) end

    local success, result = pcall(func, ...)
    if success then
        return true, result
    else
        print("[ASC Ship Core] Safe call failed: " .. tostring(result))
        return false, result
    end
end

-- Enhanced safe table access wrapper
local function SafeAccess(tbl, ...)
    if not tbl or type(tbl) ~= "table" then return nil end

    local current = tbl
    local keys = {...}

    for _, key in ipairs(keys) do
        if type(current) ~= "table" or current[key] == nil then
            return nil
        end
        current = current[key]
    end

    return current
end

-- Safe entity validation
local function SafeEntity(ent)
    return IsValid(ent) and not ent:IsWorld()
end

-- Safe player validation
local function SafePlayer(ply)
    return IsValid(ply) and ply:IsPlayer()
end

-- Safe vector validation
local function SafeVector(vec)
    return vec and isvector(vec) and not (vec.x == 0 and vec.y == 0 and vec.z == 0)
end

-- Network strings for UI
util.AddNetworkString("asc_ship_core_open_ui")
util.AddNetworkString("asc_ship_core_update_ui")
util.AddNetworkString("asc_ship_core_command")
util.AddNetworkString("asc_ship_core_open_ui")
util.AddNetworkString("asc_ship_core_close_ui")
util.AddNetworkString("asc_ship_core_update_ui")
util.AddNetworkString("asc_ship_core_name_dialog")
util.AddNetworkString("hyperdrive_play_sound")
util.AddNetworkString("asc_seat_flight_control")
util.AddNetworkString("asc_seat_flight_input")

function ENT:Initialize()
    -- Enhanced initialization with error handling
    local success, error = pcall(function()
        -- Initialize model selection system first
        self:InitializeModelSelection()

        -- Set default model (will be overridden by model selection)
        local defaultModel = self:GetSelectedModel()
        self:SetModel(defaultModel)

        self:PhysicsInit(SOLID_VPHYSICS)
        self:SetMoveType(MOVETYPE_VPHYSICS)
        self:SetSolid(SOLID_VPHYSICS)
        self:SetUseType(SIMPLE_USE)

        local phys = self:GetPhysicsObject()
        if SafeEntity(phys) then
            phys:Wake()
            phys:SetMass(50)
            phys:EnableMotion(true) -- Ensure physics object can be moved/welded
        end
    end)

    if not success then
        print("[ASC Ship Core] Initialization error: " .. tostring(error))
        -- Continue with fallback initialization
        self:SetModel("models/error.mdl") -- Fallback model
    end

    -- Enable welding and constraints
    self:SetNWBool("CanBeWelded", true)
    self:SetNWBool("AllowConstraints", true)

    -- Initialize auto-weld system with delay to prevent spawn lag
    if self.Config and self.Config.EnableAutoWeld then
        local autoWeldTimerName = "ASC_ShipCore_AutoWeld_" .. self:EntIndex()
        local delay = self.Config.AutoWeldDelay or 2
        timer.Create(autoWeldTimerName, delay, 1, function()
            if IsValid(self) then
                self:AutoWeldToNearestOwnedEntity()
            end
        end)
    end

    -- Set up for tool interactions - Allow most tools but block destructive ones
    self.CanTool = function(self, ply, trace, mode)
        if not IsValid(ply) then
            print("[ASC Ship Core] CanTool: Invalid player")
            return false
        end

        -- Check ownership first
        local owner = self:CPPIGetOwner()
        if IsValid(owner) and owner ~= ply and not ply:IsAdmin() then
            print("[ASC Ship Core] CanTool: " .. ply:Name() .. " doesn't own this ship core (Owner: " .. owner:Name() .. ")")
            return false -- Only owner or admin can use tools
        end

        -- Block destructive tools
        local blockedTools = {
            "remover", "duplicator", "material", "paint", "colour", "color",
            "ignite", "nocollide_world", "thruster", "emitter"
        }

        for _, blocked in ipairs(blockedTools) do
            if mode == blocked then
                print("[ASC Ship Core] CanTool: Blocked destructive tool '" .. mode .. "' for " .. ply:Name())
                return false
            end
        end

        -- Allow all other tools (constraint tools, wire tools, etc.)
        print("[ASC Ship Core] CanTool: Allowing tool '" .. mode .. "' for " .. ply:Name())
        return true
    end

    -- Initialize ship core data
    self.ship = nil
    self.lastUpdate = 0
    self.updateInterval = 2 -- Update every 2 seconds
    self.activeUIs = {} -- Track active UI sessions
    self.shipNameFile = "hyperdrive/ship_names_" .. self:EntIndex() .. ".txt"

    -- Ambient sound system removed per user request

    -- Track spawn time for initialization delay
    self.SpawnTime = CurTime()
    self.InitializationComplete = false

    -- Load ship name from file
    self:LoadShipName()

    -- Initialize systems with longer delay to prevent spawn lag
    timer.Simple(3, function()
        if IsValid(self) then
            self:InitializeSystems()
        end
    end)

    -- Initialize modern UI integration
    self.UIData = {
        lastUpdate = 0,
        updateInterval = 0.5,
        notifications = {},
        theme = "modern",
        activeTab = "overview",
        animationState = {},
        fadeAlpha = 255,
        targetAlpha = 255
    }

    -- v2.2.0 Real-Time Features
    self.FleetID = 0
    self.FleetRole = ""
    self.RealTimeMonitoring = true
    self.LastRealTimeUpdate = 0
    self.RealTimeUpdateRate = 2.0 -- Reduced: 0.5 FPS for real-time updates (was 20 FPS)
    self.PerformanceMetrics = {}
    self.SystemAlerts = {}
    self.AlertCooldowns = {} -- Track cooldowns for different alert types
    self.AlertHistory = {} -- Track alert history to prevent spam
    self.AdminAccess = false

    -- Optimized update system with reduced frequencies to prevent spawn lag
    self.LastEntityScan = 0
    self.EntityScanRate = 2.0 -- Reduced: 0.5 FPS for entity scanning (was 10 FPS)
    self.LastResourceUpdate = 0
    self.ResourceUpdateRate = 1.0 -- Reduced: 1 FPS for resource calculations (was 5 FPS)
    self.LastSystemCheck = 0
    self.SystemCheckRate = 3.0 -- Reduced: 0.33 FPS for system health checks (was 2 FPS)
    self.LastNetworkUpdate = 0
    self.NetworkUpdateRate = 1.0 -- Reduced: 1 FPS for network synchronization (was 10 FPS)

    -- Smart update tracking
    self.UpdatePriorities = {
        entity_scan = 1,     -- High priority
        resource_update = 2, -- Medium priority
        system_check = 3,    -- Low priority
        network_sync = 1     -- High priority
    }

    -- Performance optimization settings
    self.PerformanceMode = false
    self.LastPerformanceCheck = 0
    self.PerformanceCheckInterval = 5.0 -- Check every 5 seconds
    self.FrameTimeHistory = {}
    self.MaxFrameHistory = 30

    -- Adaptive update rates based on activity
    self.AdaptiveRates = {
        entity_scan = {min = 0.05, max = 1.0, current = 0.1},
        resource_update = {min = 0.1, max = 2.0, current = 0.2},
        system_check = {min = 0.25, max = 5.0, current = 0.5},
        network_sync = {min = 0.05, max = 0.5, current = 0.1}
    }

    -- Change detection for smart updates
    self.LastEntityCount = 0
    self.LastResourceState = {}
    self.LastSystemState = {}
    self.ChangeDetectionEnabled = true

    -- Performance tracking
    self.UpdatePerformance = {
        entity_scan = {total_time = 0, call_count = 0, avg_time = 0},
        resource_update = {total_time = 0, call_count = 0, avg_time = 0},
        system_check = {total_time = 0, call_count = 0, avg_time = 0},
        network_sync = {total_time = 0, call_count = 0, avg_time = 0}
    }

    -- Real-time data caches
    self.CachedAttachedEntities = {}
    self.CachedResourceData = {}
    self.CachedSystemStatus = {}
    self.CachedPerformanceData = {}

    -- Register with master scheduler instead of using timers
    timer.Simple(5, function()
        if IsValid(self) then
            self:InitializeFleetManagement()
            self:InitializeRealTimeMonitoring()
            self:InitializePerformanceAnalytics()
            self:RegisterWithMasterScheduler()
        end
    end)

    print("[ASC Ship Core] Enhanced ASC Ship Core v5.1.0 with Stargate hyperspace integration and advanced features initialized at " .. tostring(self:GetPos()))

    -- Register this ship core with both class names for compatibility
    self:RegisterDualClassCompatibility()
end

-- Register ship core with dual class compatibility
function ENT:RegisterDualClassCompatibility()
    -- Make this entity respond to both "asc_ship_core" and "ship_core" class checks
    local originalGetClass = self.GetClass
    self.GetClass = function(self, requestedClass)
        local actualClass = originalGetClass(self)

        -- If someone specifically asks for "ship_core", return that for compatibility
        if requestedClass == "ship_core" then
            return "ship_core"
        end

        -- Otherwise return the actual class
        return actualClass
    end

    -- Add compatibility flag
    self.IsCompatibleShipCore = true
    self.SupportsLegacyClass = true

    print("[ASC Ship Core] Dual class compatibility enabled (asc_ship_core + ship_core)")
end

-- Override GetClass to support both class names
function ENT:GetClass()
    -- Return the actual class name
    return "asc_ship_core"
end

-- Compatibility function for legacy systems
function ENT:IsShipCore()
    return true
end

-- Compatibility function to check if this is a valid ship core (both classes)
function ENT:IsValidShipCoreClass()
    local class = self:GetClass()
    return class == "asc_ship_core" or class == "ship_core"
end

-- Global console command to fix invalid ship cores
if SERVER then
    concommand.Add("asc_fix_invalid_ship_cores", function(ply, cmd, args)
        if not IsValid(ply) or not ply:IsAdmin() then
            if IsValid(ply) then
                ply:ChatPrint("[ASC] Only admins can use this command")
            end
            return
        end

        local fixedCount = 0
        local totalCores = 0

        -- Find all ASC ship cores
        for _, ent in ipairs(ents.FindByClass("asc_ship_core")) do
            if IsValid(ent) then
                totalCores = totalCores + 1
                local state = ent:GetState()

                if state == ent.States.INVALID or not ent:GetCoreValid() then
                    ent:AttemptCoreValidationFix()
                    fixedCount = fixedCount + 1
                    print("[ASC] Fixed invalid ship core at " .. tostring(ent:GetPos()))
                end
            end
        end

        ply:ChatPrint("[ASC] Fixed " .. fixedCount .. " invalid ship cores out of " .. totalCores .. " total cores")
        print("[ASC] Ship core fix command completed: " .. fixedCount .. "/" .. totalCores .. " cores fixed")
    end)

    -- Console command to force all ship cores valid
    concommand.Add("asc_force_all_cores_valid", function(ply, cmd, args)
        if not IsValid(ply) or not ply:IsAdmin() then
            if IsValid(ply) then
                ply:ChatPrint("[ASC] Only admins can use this command")
            end
            return
        end

        local fixedCount = 0

        -- Force all ASC ship cores to be valid
        for _, ent in ipairs(ents.FindByClass("asc_ship_core")) do
            if IsValid(ent) then
                ent:SetCoreValid(true)
                ent:SetState(ent.States.ACTIVE)
                ent:SetStatusMessage("Forced valid by admin")
                fixedCount = fixedCount + 1
            end
        end

        ply:ChatPrint("[ASC] Forced " .. fixedCount .. " ship cores to valid state")
        print("[ASC] Force valid command completed: " .. fixedCount .. " cores forced valid")
    end)

    -- Console command to check ship core compatibility
    concommand.Add("asc_check_ship_core_compatibility", function(ply, cmd, args)
        if not IsValid(ply) then return end

        local trace = ply:GetEyeTrace()
        if not IsValid(trace.Entity) then
            ply:ChatPrint("[ASC] Look at a ship core to check compatibility")
            return
        end

        local ent = trace.Entity
        local entClass = ent:GetClass()

        if entClass == "asc_ship_core" then
            ply:ChatPrint("[ASC] ✓ ASC Ship Core - Fully compatible")
            ply:ChatPrint("[ASC] Supports: Auto-weld, dual class detection, all features")

            if ent.IsCompatibleShipCore then
                ply:ChatPrint("[ASC] ✓ Dual class compatibility enabled")
            end

            if ent.SupportsLegacyClass then
                ply:ChatPrint("[ASC] ✓ Legacy ship_core class support enabled")
            end

        elseif entClass == "ship_core" then
            ply:ChatPrint("[ASC] ⚠ Legacy Ship Core detected")

            if ent.IsLegacyShipCore then
                ply:ChatPrint("[ASC] ✓ This is a compatibility entity - will auto-upgrade to ASC")
            else
                ply:ChatPrint("[ASC] ⚠ Old ship core - consider upgrading to ASC Ship Core")
            end

        else
            ply:ChatPrint("[ASC] ✗ Not a ship core (found: " .. entClass .. ")")
        end
    end)
end

-- Ambient Sound System Functions - REMOVED per user request

function ENT:OnRemove()
    -- Unregister from master scheduler first
    self:UnregisterFromMasterScheduler()

    -- Clean up resource system (SB3Resources cleanup is handled automatically by hook)
    if HYPERDRIVE.SB3Resources and HYPERDRIVE.SB3Resources.CoreStorage then
        local coreId = self:EntIndex()
        HYPERDRIVE.SB3Resources.CoreStorage[coreId] = nil
    end

    -- Clean up basic resource timer
    local timerName = "ASC_BasicResourceRegen_" .. self:EntIndex()
    if timer.Exists(timerName) then
        timer.Remove(timerName)
    end

    -- Remove life support from players
    for _, ply in ipairs(player.GetAll()) do
        if IsValid(ply) and ply:GetNWEntity("LifeSupportCore") == self then
            ply:SetNWBool("HasLifeSupport", false)
            ply:SetNWEntity("LifeSupportCore", NULL)
        end
    end

    -- Clean up ship core from ship system
    if HYPERDRIVE.ShipCore and HYPERDRIVE.ShipCore.Ships then
        local coreId = self:EntIndex()
        HYPERDRIVE.ShipCore.Ships[coreId] = nil
    end

    -- Clean up optimization system resources
    if ASC and ASC.ShipCore and ASC.ShipCore.Optimization then
        local coreId = self:EntIndex()

        -- Clean up incremental detection queue
        if ASC.ShipCore.Optimization.IncrementalDetection.queues[coreId] then
            ASC.ShipCore.Optimization.IncrementalDetection.queues[coreId] = nil
        end

        -- Clean up relationship mapping
        ASC.ShipCore.Optimization.RelationshipMap.dirtyEntities[coreId] = nil
        ASC.ShipCore.Optimization.RelationshipMap.constraints[coreId] = nil
        ASC.ShipCore.Optimization.RelationshipMap.parents[coreId] = nil
        ASC.ShipCore.Optimization.RelationshipMap.children[coreId] = nil

        -- Clean up constraint cache
        local cache = ASC.ShipCore.Optimization.ConstraintCache
        cache.cache[coreId] = nil
        cache.timestamps[coreId] = nil
        cache.accessCount[coreId] = nil

        -- Remove from spatial grid
        local grid = ASC.ShipCore.Optimization.SpatialGrid
        local cellKey = grid.entityToCell[coreId]
        if cellKey and grid.cells[cellKey] then
            for i, ent in ipairs(grid.cells[cellKey]) do
                if ent == self then
                    table.remove(grid.cells[cellKey], i)
                    break
                end
            end
        end
        grid.entityToCell[coreId] = nil
    end

    -- Clean up other resources
    if self.ship then
        print("[ASC Ship Core] ASC ship core removed, cleaning up ship data")
    end

    print("[ASC Ship Core] Resource cleanup completed")
end

function ENT:EmergencyResourceShutdown()
    -- Emergency shutdown of all resource systems
    if HYPERDRIVE.SB3Resources then
        local storage = HYPERDRIVE.SB3Resources.GetCoreStorage(self)
        if storage then
            storage.emergencyMode = true
            storage.lifeSupportActive = false
            print("[ASC Ship Core] Emergency resource shutdown activated")
        end
    elseif self.BasicResourceStorage then
        -- Stop basic resource regeneration
        local timerName = "ASC_BasicResourceRegen_" .. self:EntIndex()
        if timer.Exists(timerName) then
            timer.Remove(timerName)
        end

        self:SetNWBool("ResourceEmergency", true)
        self:SetNWBool("LifeSupportActive", false)
        print("[ASC Ship Core] Emergency shutdown - basic resource system halted")
    end

    -- Notify players
    for _, ply in ipairs(player.GetAll()) do
        if IsValid(ply) and ply:GetNWEntity("LifeSupportCore") == self then
            ply:ChatPrint("⚠️ EMERGENCY: Ship core resource systems have been shut down!")
            ply:SetNWBool("HasLifeSupport", false)
        end
    end
end

function ENT:InitializeSystems()
    -- Detect ship
    self:UpdateShipDetection()

    -- Initialize resource system first (required for other systems)
    if HYPERDRIVE.SB3Resources then
        local success = HYPERDRIVE.SB3Resources.InitializeCoreStorage(self)
        if success then
            self:SetNWBool("ResourceSystemActive", true)
            self:SetNWBool("AutoProvisionEnabled", true)
            self:SetNWBool("WeldDetectionEnabled", true)
            self:SetNWString("LastResourceActivity", "System initialized")
            print("[ASC Ship Core] Resource system initialized")
        else
            -- Try to initialize without Spacebuild 3 integration
            print("[ASC Ship Core] Spacebuild 3 not available, initializing basic resource system")
            self:InitializeBasicResourceSystem()
        end
    else
        print("[ASC Ship Core] SB3Resources not available, initializing basic resource system")
        self:InitializeBasicResourceSystem()
    end

    -- Initialize hull damage system
    if HYPERDRIVE.HullDamage and self.ship then
        local createFunc = SafeAccess(HYPERDRIVE.HullDamage, "CreateHullSystem")
        if createFunc then
            local hull, message = SafeCall(createFunc, self.ship, self)
            if hull then
                self:SetHullSystemActive(true)
                print("[ASC Ship Core] Hull damage system initialized: " .. (message or "Success"))
            else
                print("[ASC Ship Core] Hull damage system failed: " .. (message or "Unknown error"))
            end
        else
            print("[ASC Ship Core] Hull damage CreateHullSystem function not available")
        end
    end

    -- Initialize built-in shield system (no generators needed) - delayed to prevent spawn lag
    if ASC and ASC.Shields and ASC.Shields.Core and ASC.Shields.Core.Initialize then
        timer.Simple(8, function()
            if IsValid(self) then
                local success, message = ASC.Shields.Core.Initialize(self, "ADVANCED")
                if success then
                    self:SetShieldSystemActive(true)
                    print("[ASC Ship Core] Built-in shield system initialized: " .. (message or "Success"))
                else
                    print("[ASC Ship Core] Built-in shield system failed: " .. (message or "Unknown error"))
                end
            end
        end)
    elseif HYPERDRIVE.Shields and self.ship then
        -- Fallback to old shield system
        local createFunc = SafeAccess(HYPERDRIVE.Shields, "CreateShield")
        if createFunc then
            local shield, message = SafeCall(createFunc, self, self.ship)
            if shield then
                self:SetShieldSystemActive(true)
                print("[ASC Ship Core] Legacy shield system initialized: " .. (message or "Success"))
            else
                print("[ASC Ship Core] Legacy shield system failed: " .. (message or "Unknown error"))
            end
        else
            print("[ASC Ship Core] Shield CreateShield function not available")
        end
    end

    -- Initialize enhanced flight system - delayed to prevent spawn lag
    timer.Simple(3, function()
        if IsValid(self) then
            self:InitializeEnhancedFlightSystem()
        end
    end)

    -- Initialize built-in hull system - delayed to prevent spawn lag
    timer.Simple(4, function()
        if IsValid(self) then
            self:InitializeBuiltInHullSystem()
        end
    end)

    -- Initialize combat system - delayed to prevent spawn lag
    timer.Simple(5, function()
        if IsValid(self) then
            self:InitializeCombatSystem()
        end
    end)

    -- Initialize CAP integration
    self:InitializeCAPIntegration()

    -- v2.2.1 Initialize new systems
    self:InitializeV221Systems()

    -- Activation effects removed per user request
end

-- v2.2.1 Initialize new systems
function ENT:InitializeV221Systems()
    -- Initialize flight system
    if HYPERDRIVE.Flight and self.ship then
        local flightSystem = HYPERDRIVE.Flight.GetFlightSystem(self)
        if not flightSystem then
            flightSystem = HYPERDRIVE.Flight.CreateFlightSystem(self)
        end

        if flightSystem then
            self.flightSystemInitialized = true
            self:SetNWBool("FlightSystemActive", true)
            print("[ASC Ship Core] Flight system initialized")
        end
    end

    -- Initialize weapon systems with delay to prevent spawn lag
    timer.Simple(15, function()
        if IsValid(self) and HYPERDRIVE.Weapons and self.ship then
            -- Find nearby weapons and link them
            local nearbyWeapons = ents.FindInSphere(self:GetPos(), 2000)
            local weaponCount = 0

            for _, ent in ipairs(nearbyWeapons) do
                if IsValid(ent) and string.find(ent:GetClass(), "hyperdrive_") and ent.weapon then
                    ent.shipCore = self
                    weaponCount = weaponCount + 1
                end
            end

            if weaponCount > 0 then
                self.weaponSystemsInitialized = true
                self:SetNWBool("WeaponSystemsActive", true)
                self:SetNWInt("WeaponCount", weaponCount)
                print("[ASC Ship Core] Weapon systems initialized - " .. weaponCount .. " weapons linked")
            end
        end
    end)

    -- Initialize ammunition system
    if HYPERDRIVE.Ammunition and self.ship then
        local storage = HYPERDRIVE.Ammunition.CreateStorage(self, 10000) -- 10kg capacity
        if storage then
            self.ammunitionSystemInitialized = true
            self:SetNWBool("AmmunitionSystemActive", true)
            print("[ASC Ship Core] Ammunition system initialized")
        end
    end

    -- Initialize tactical AI
    if HYPERDRIVE.TacticalAI and self.ship then
        local ai = HYPERDRIVE.TacticalAI.CreateAI(self, "balanced")
        if ai then
            self.tacticalAIInitialized = true
            self:SetNWBool("TacticalAIActive", true)
            print("[ASC Ship Core] Tactical AI initialized")
        end
    end

    -- Initialize docking system compatibility
    self.dockingSystemInitialized = true
    self:SetNWBool("DockingSystemActive", true)

    -- Initialize welding detection system with delay to prevent spawn lag
    timer.Simple(12, function()
        if IsValid(self) then
            self:InitializeWeldingDetection()
        end
    end)

    -- Auto-weld system removed for performance and simplicity

    print("[ASC Ship Core] v2.2.1 systems initialization complete")
end

-- Initialize CAP integration
function ENT:InitializeCAPIntegration()
    -- CAP integration state
    self.CAPIntegrationActive = false
    self.LastCAPUpdate = 0
    self.CAPUpdateInterval = 2.0
    self.CAPIntegration = {}

    -- Enhanced configuration
    self.Config = self.Config or {}
    self.Config.EnableCAPIntegration = true
    self.Config.PreferCAPSystems = true
    self.Config.AutoCreateCAPShields = false
    self.Config.CAPResourceIntegration = true
    self.Config.CAPEffectsEnabled = true
    self.Config.CAPShieldAutoActivation = true
    self.Config.EnableAutoWeld = true -- Auto-weld to nearest owned entity
    self.Config.AutoWeldRadius = 500 -- Search radius for auto-weld
    self.Config.AutoWeldDelay = 2 -- Delay before auto-weld (seconds)

    -- Initialize CAP status network variables with delay to prevent spawn lag
    timer.Simple(7, function()
        if IsValid(self) then
            self:SetNWBool("CAPIntegrationActive", false)
            self:SetNWBool("CAPShieldsDetected", false)
            self:SetNWBool("CAPEnergyDetected", false)
            self:SetNWBool("CAPResourcesDetected", false)
            self:SetNWString("CAPStatus", "Detecting...")
            self:SetNWFloat("CAPEnergyLevel", 0)
            self:SetNWInt("CAPShieldCount", 0)
            self:SetNWInt("CAPEntityCount", 0)
            self:SetNWString("CAPVersion", "Unknown")
        end
    end)

    if not self.Config.EnableCAPIntegration then
        self:SetNWString("CAPStatus", "CAP Integration Disabled")
        return
    end

    if not HYPERDRIVE.CAP or not HYPERDRIVE.CAP.Available then
        self:SetNWString("CAPStatus", "CAP Not Available")
        return
    end

    self.CAPIntegrationActive = true
    self:SetNWBool("CAPIntegrationActive", true)

    -- Get CAP version info
    local detection = HYPERDRIVE.CAP.Detection
    if detection then
        self:SetNWString("CAPVersion", detection.version or "Unknown")
        self:SetNWString("CAPStatus", "CAP Integration Active - " .. detection.version)
    else
        self:SetNWString("CAPStatus", "CAP Integration Active")
    end

    -- Initialize CAP subsystems
    self.CAPIntegration = {
        shields = {},
        energySources = {},
        resources = {},
        lastShieldUpdate = 0,
        lastEnergyUpdate = 0,
        lastResourceUpdate = 0
    }

    print("[ASC Ship Core] CAP integration initialized for core " .. self:EntIndex())
end

-- Register with master scheduler for optimized updates
function ENT:RegisterWithMasterScheduler()
    if not ASC or not ASC.MasterScheduler then return end

    local coreId = self:EntIndex()

    -- Register high priority tasks (critical systems)
    ASC.MasterScheduler.RegisterTask("ShipCore_EntityScan_" .. coreId, "High", function()
        if IsValid(self) then self:OptimizedEntityScan() end
    end)

    ASC.MasterScheduler.RegisterTask("ShipCore_NetworkSync_" .. coreId, "High", function()
        if IsValid(self) then self:RealTimeNetworkSync() end
    end)

    -- Register medium priority tasks (normal operations)
    ASC.MasterScheduler.RegisterTask("ShipCore_ResourceUpdate_" .. coreId, "Medium", function()
        if IsValid(self) then self:RealTimeResourceUpdate() end
    end)

    ASC.MasterScheduler.RegisterTask("ShipCore_SystemUpdate_" .. coreId, "Medium", function()
        if IsValid(self) then self:UpdateSystems() end
    end)

    -- Register low priority tasks (background operations)
    ASC.MasterScheduler.RegisterTask("ShipCore_SystemCheck_" .. coreId, "Low", function()
        if IsValid(self) then self:RealTimeSystemCheck() end
    end)

    ASC.MasterScheduler.RegisterTask("ShipCore_MonitoringUpdate_" .. coreId, "Low", function()
        if IsValid(self) then self:UpdateRealTimeData() end
    end)

    print("[ASC Ship Core] Registered with master scheduler: " .. coreId)
end

-- Unregister from master scheduler on removal
function ENT:UnregisterFromMasterScheduler()
    if not ASC or not ASC.MasterScheduler then return end

    local coreId = self:EntIndex()
    local tasks = {
        "ShipCore_EntityScan_" .. coreId,
        "ShipCore_NetworkSync_" .. coreId,
        "ShipCore_ResourceUpdate_" .. coreId,
        "ShipCore_SystemUpdate_" .. coreId,
        "ShipCore_SystemCheck_" .. coreId,
        "ShipCore_MonitoringUpdate_" .. coreId
    }

    for _, taskName in ipairs(tasks) do
        ASC.MasterScheduler.UnregisterTask(taskName)
    end
end

function ENT:Think()
    local currentTime = CurTime()

    -- Skip all updates for configurable time after spawn to prevent lag
    local spawnDelay = GetConVar("asc_spawn_delay"):GetFloat()
    if not self.InitializationComplete and currentTime - (self.SpawnTime or 0) < spawnDelay then
        self:NextThink(currentTime + 1.0) -- Increased think rate during initialization
        return true
    end

    if not self.InitializationComplete then
        self.InitializationComplete = true
        print("[ASC Ship Core] Initialization complete, starting normal operations")
    end

    -- Minimal Think function - most work is now handled by master scheduler
    -- Only handle critical immediate updates here

    -- Check for performance mode changes
    local performanceMode = GetConVar("asc_performance_mode"):GetBool()
    if performanceMode and not self.PerformanceMode then
        self:EnablePerformanceMode()
    elseif not performanceMode and self.PerformanceMode then
        self:DisablePerformanceMode()
    end

    -- Update UI at reduced frequency
    if currentTime - (self.lastUIUpdate or 0) > 1.0 then
        self:UpdateUI()
        self.lastUIUpdate = currentTime
    end

    -- Much slower think rate since master scheduler handles most updates
    self:NextThink(currentTime + 1.0) -- 1 FPS think rate
    return true
end

function ENT:UpdateSystems()
    -- Update ship detection
    self:UpdateShipDetection()

    -- Update enhanced power management system
    self:UpdatePowerManagement()

    -- Update hull damage system
    self:UpdateHullSystem()

    -- Update shield system with power dependency
    self:UpdateShieldSystem()

    -- Update resource system (Spacebuild 3 integration)
    self:UpdateResourceSystem()

    -- Update CAP integration
    self:UpdateCAPStatus()

    -- v2.2.1 Update new systems
    self:UpdateV221Systems()

    -- Update crew efficiency if players are aboard
    self:UpdateCrewEfficiency()

    -- Update core state
    self:UpdateCoreState()
end

-- v2.2.1 Update new systems
function ENT:UpdateV221Systems()
    if not self.ship then return end

    -- Update flight system status
    if self.flightSystemInitialized and HYPERDRIVE.Flight then
        local flightSystem = HYPERDRIVE.Flight.GetFlightSystem(self)
        if flightSystem then
            local status = flightSystem:GetStatus()
            self:SetNWBool("FlightActive", status.active)
            self:SetNWFloat("FlightSpeed", status.velocity)
            self:SetNWString("FlightMode", status.flightMode)
        end
    end

    -- Update weapon systems status
    if self.weaponSystemsInitialized and HYPERDRIVE.Weapons then
        local weaponCount = 0
        local activeWeapons = 0

        for _, ent in ipairs(ents.FindInSphere(self:GetPos(), 2000)) do
            if IsValid(ent) and string.find(ent:GetClass(), "hyperdrive_") and ent.weapon then
                weaponCount = weaponCount + 1
                if ent.weapon.active then
                    activeWeapons = activeWeapons + 1
                end
            end
        end

        self:SetNWInt("WeaponCount", weaponCount)
        self:SetNWInt("ActiveWeapons", activeWeapons)
    end

    -- Update ammunition system status
    if self.ammunitionSystemInitialized and HYPERDRIVE.Ammunition then
        local storage = HYPERDRIVE.Ammunition.GetStorage(self)
        if storage and storage.ammunition then
            local totalAmmo = 0
            for ammoType, amount in pairs(storage.ammunition) do
                totalAmmo = totalAmmo + amount
            end
            self:SetNWInt("TotalAmmunition", totalAmmo)
            self:SetNWFloat("AmmoCapacity", storage.capacity or 0)
        else
            -- Set defaults if no ammunition storage
            self:SetNWInt("TotalAmmunition", 0)
            self:SetNWFloat("AmmoCapacity", 0)
        end
    end

    -- Update tactical AI status
    if self.tacticalAIInitialized and HYPERDRIVE.TacticalAI then
        local ai = HYPERDRIVE.TacticalAI.GetAI(self)
        if ai then
            local status = ai:GetStatus()
            self:SetNWBool("TacticalAIActive", status.active)
            self:SetNWString("TacticalState", status.tacticalState)
            self:SetNWInt("ThreatCount", status.threatCount)
        end
    end
end

-- Update CAP integration status - Enhanced v3.0
function ENT:UpdateCAPStatus()
    if not self.CAPIntegrationActive then return end

    local currentTime = CurTime()
    if currentTime - self.LastCAPUpdate < self.CAPUpdateInterval then return end
    self.LastCAPUpdate = currentTime

    -- Get ship
    if not self.ship then return end

    local capEntityCount = 0
    local enhancedIntegration = ASC.CAP.Enhanced and ASC.CAP.Enhanced.Config.EnableEnhancedIntegration

    -- Enhanced CAP entity detection
    if enhancedIntegration then
        local capEntities = ASC.CAP.Enhanced.DetectCAPEntities()
        local shipEntities = {}

        -- Filter CAP entities that belong to this ship
        for _, entityData in ipairs(capEntities) do
            if IsValid(entityData.entity) and self.ship.entities then
                for _, shipEnt in ipairs(self.ship.entities) do
                    if entityData.entity == shipEnt then
                        table.insert(shipEntities, entityData)
                        capEntityCount = capEntityCount + 1
                        break
                    end
                end
            end
        end

        self.CAPIntegration.enhancedEntities = shipEntities

        -- Enhanced resource bridging
        if ASC.CAP.Enhanced.ResourceBridge then
            ASC.CAP.Enhanced.ResourceBridge.BridgeResources(self, shipEntities)
        end
    end

    -- Update CAP shield status
    if HYPERDRIVE.CAP.Shields then
        local shields = HYPERDRIVE.CAP.Shields.FindShields(self.ship)
        self.CAPIntegration.shields = shields
        self:SetNWBool("CAPShieldsDetected", #shields > 0)
        self:SetNWInt("CAPShieldCount", #shields)

        if not enhancedIntegration then
            capEntityCount = capEntityCount + #shields
        end

        if #shields > 0 then
            local shieldStatus = HYPERDRIVE.CAP.Shields.GetStatus(self, self.ship)
            if shieldStatus then
                self:SetNWFloat("ShieldStrength", shieldStatus.averageStrength or 0)
                self:SetNWBool("ShieldSystemActive", shieldStatus.activeShields > 0)
            end
        end
    end

    -- Update CAP energy status
    if HYPERDRIVE.CAP.Resources then
        local energySources = HYPERDRIVE.CAP.Resources.FindEnergySources(self.ship)
        self.CAPIntegration.energySources = energySources
        self:SetNWBool("CAPEnergyDetected", #energySources > 0)

        if not enhancedIntegration then
            capEntityCount = capEntityCount + #energySources
        end

        if #energySources > 0 then
            local totalEnergy = HYPERDRIVE.CAP.Resources.GetTotalEnergy and
                               HYPERDRIVE.CAP.Resources.GetTotalEnergy(self.ship) or 0
            self:SetNWFloat("CAPEnergyLevel", totalEnergy)
        end
    end

    -- Legacy CAP resource detection (if enhanced integration is disabled)
    if not enhancedIntegration and self.ship.entities then
        for _, ent in ipairs(self.ship.entities) do
            if IsValid(ent) and HYPERDRIVE.CAP.GetEntityCategory then
                local category = HYPERDRIVE.CAP.GetEntityCategory(ent:GetClass())
                if category then
                    capEntityCount = capEntityCount + 1
                end
            end
        end
    end

    -- Update network variables
    self:SetNWBool("CAPResourcesDetected", capEntityCount > 0)
    self:SetNWInt("CAPEntityCount", capEntityCount)

    -- Enhanced integration status
    if enhancedIntegration then
        self:SetNWBool("CAPEnhancedIntegration", true)
        local technology = ASC.CAP.Enhanced.GetBestAvailableTechnology(self:GetOwner())
        self:SetNWString("CAPTechnology", technology or "Tau_ri")
    else
        self:SetNWBool("CAPEnhancedIntegration", false)
        self:SetNWString("CAPTechnology", "Unknown")
    end
end

function ENT:UpdateShipDetection()
    if not HYPERDRIVE.ShipCore then
        self:SetShipDetected(false)
        self:SetShipType("Ship Core System Not Available")
        self:SetCoreValid(false)
        self:SetStatusMessage("Ship Core system not loaded")
        return
    end

    -- Check for duplicate ship cores first
    self:CheckForDuplicateShipCores()

    -- Get or create ship for this core (optimized)
    local ship = HYPERDRIVE.ShipCore.GetShip(self)
    if not ship then
        -- Create ship with this core as the center using optimized detection
        ship = self:CreateOptimizedShip()
    end

    if ship then
        -- Update ship data
        ship:Update()

        self.ship = ship
        self:SetShipDetected(true)
        self:SetShipType(ship:GetShipType())
        self:SetShipCenter(ship:GetCenter())
        self:SetFrontDirection(ship:GetFrontDirection())

        -- Validate core uniqueness with improved error handling
        if HYPERDRIVE.ShipCore.ValidateShipCoreUniqueness then
            local valid, message = HYPERDRIVE.ShipCore.ValidateShipCoreUniqueness(self)
            self:SetCoreValid(valid)
            if not valid then
                print("[ASC Ship Core] Core validation failed: " .. message)
                self:SetStatusMessage("CORE ISSUE: " .. message)
                -- Don't set to INVALID immediately, try to fix the issue
                timer.Simple(1, function()
                    if IsValid(self) then
                        self:AttemptCoreValidationFix()
                    end
                end)
                return
            end
        else
            -- If validation system not available, assume valid
            self:SetCoreValid(true)
        end

        self:SetStatusMessage("Ship detected: " .. ship:GetShipType())
        print("[ASC Ship Core] Ship detected: " .. ship:GetShipType() .. " with " .. #ship:GetEntities() .. " entities")
    else
        self.ship = nil
        self:SetShipDetected(false)
        self:SetShipType("No Ship")
        self:SetCoreValid(false)
        self:SetStatusMessage("Ship detection failed")
        print("[ASC Ship Core] Failed to create or detect ship")
    end
end

function ENT:UpdateHullSystem()
    if not HYPERDRIVE.HullDamage or not self.ship then
        self:SetHullSystemActive(false)
        self:SetHullIntegrity(100)
        return
    end

    local getStatusFunc = SafeAccess(HYPERDRIVE.HullDamage, "GetHullStatus")
    if getStatusFunc then
        local hullStatus = SafeCall(getStatusFunc, self)
        if hullStatus then
            self:SetHullSystemActive(true)
            self:SetHullIntegrity(math.floor(hullStatus.integrityPercent or 100))

            -- Update status based on hull integrity
            if hullStatus.emergencyMode then
                self:SetState(self.States.EMERGENCY)
                self:SetStatusMessage("HULL EMERGENCY: " .. math.floor(hullStatus.integrityPercent) .. "%")
            elseif hullStatus.criticalMode then
                self:SetState(self.States.CRITICAL)
                self:SetStatusMessage("HULL CRITICAL: " .. math.floor(hullStatus.integrityPercent) .. "%")
            end
        else
            self:SetHullSystemActive(false)
            self:SetHullIntegrity(100)
        end
    else
        self:SetHullSystemActive(false)
        self:SetHullIntegrity(100)
    end
end

function ENT:UpdateShieldSystem()
    if not HYPERDRIVE.Shields or not self.ship then
        self:SetShieldSystemActive(false)
        self:SetShieldStrength(0)
        return
    end

    local getStatusFunc = SafeAccess(HYPERDRIVE.Shields, "GetShieldStatus")
    if getStatusFunc then
        local shieldStatus = SafeCall(getStatusFunc, self)
        if shieldStatus then
            self:SetShieldSystemActive(shieldStatus.available)
            self:SetShieldStrength(math.floor(shieldStatus.strengthPercent or 0))
        else
            self:SetShieldSystemActive(false)
            self:SetShieldStrength(0)
        end
    else
        self:SetShieldSystemActive(false)
        self:SetShieldStrength(0)
    end
end

function ENT:UpdateResourceSystem()
    -- Update Spacebuild 3 resource system if available
    if HYPERDRIVE.SB3Resources and HYPERDRIVE.SB3Resources.UpdateCoreResources then
        HYPERDRIVE.SB3Resources.UpdateCoreResources(self)
    end

    -- Update resource-related network variables
    self:UpdateResourceNetworkVars()
end

-- Handle UI commands
function ENT:HandleUICommand(ply, command, data)
    if not IsValid(ply) then return end

    if command == "set_ship_name" then
        local newName = data.name or "Unnamed Ship"
        self:SetShipName(newName)
        self:SaveShipName()
        ply:ChatPrint("[ASC Ship Core] Ship name set to: " .. newName)

    elseif command == "repair_hull" then
        if HYPERDRIVE.HullDamage and HYPERDRIVE.HullDamage.RepairHull then
            local success, message = HYPERDRIVE.HullDamage.RepairHull(self, data.amount or 25)
            if success then
                ply:ChatPrint("[ASC Ship Core] Hull repaired: " .. (message or "Success"))
            else
                ply:ChatPrint("[ASC Ship Core] Hull repair failed: " .. (message or "Unknown error"))
            end
        end

    elseif command == "toggle_shields" then
        if HYPERDRIVE.Shields and HYPERDRIVE.Shields.ToggleShields then
            local success, message = HYPERDRIVE.Shields.ToggleShields(self)
            if success then
                ply:ChatPrint("[ASC Ship Core] Shields toggled: " .. (message or "Success"))
            else
                ply:ChatPrint("[ASC Ship Core] Shield toggle failed: " .. (message or "Unknown error"))
            end
        end

    elseif command == "mute_ambient" then
        -- Ambient sound system removed per user request
        ply:ChatPrint("[ASC Ship Core] Ambient sound system has been removed")



    -- Enhanced CAP integration commands
    elseif command == "cap_control_entity" then
        if ASC.CAP.Communication then
            local entityIndex = data.entity_index
            local action = data.action
            local parameters = data.parameters or {}

            local entity = Entity(entityIndex)
            if IsValid(entity) then
                local success, result = ASC.CAP.Communication.ControlEntity(entity, action, parameters)
                if success then
                    ply:ChatPrint("[ASC Ship Core] CAP entity controlled: " .. (result or "Success"))
                else
                    ply:ChatPrint("[ASC Ship Core] CAP control failed: " .. (result or "Unknown error"))
                end
            else
                ply:ChatPrint("[ASC Ship Core] Invalid CAP entity")
            end
        else
            ply:ChatPrint("[ASC Ship Core] CAP communication system not available")
        end

    elseif command == "cap_batch_control" then
        if ASC.CAP.Communication and self.CAPIntegration and self.CAPIntegration.enhancedEntities then
            local action = data.action
            local parameters = data.parameters or {}
            local category = data.category -- Optional: filter by category

            local entities = {}
            for _, entityData in ipairs(self.CAPIntegration.enhancedEntities) do
                if IsValid(entityData.entity) then
                    if not category or entityData.category == category then
                        table.insert(entities, entityData.entity)
                    end
                end
            end

            if #entities > 0 then
                local success, results = ASC.CAP.Communication.BatchControl(entities, action, parameters)
                if success then
                    local successCount = 0
                    for _, result in ipairs(results) do
                        if result.success then successCount = successCount + 1 end
                    end
                    ply:ChatPrint(string.format("[ASC Ship Core] CAP batch control: %d/%d entities succeeded", successCount, #entities))
                else
                    ply:ChatPrint("[ASC Ship Core] CAP batch control failed")
                end
            else
                ply:ChatPrint("[ASC Ship Core] No CAP entities found for batch control")
            end
        else
            ply:ChatPrint("[ASC Ship Core] CAP batch control not available")
        end

    elseif command == "cap_activate_shields" then
        if HYPERDRIVE.CAP.Shields then
            local success, message = HYPERDRIVE.CAP.Shields.Activate(self, self.ship, "manual")
            if success then
                ply:ChatPrint("[ASC Ship Core] CAP shields activated: " .. (message or "Success"))
            else
                ply:ChatPrint("[ASC Ship Core] CAP shield activation failed: " .. (message or "Unknown error"))
            end
        else
            ply:ChatPrint("[ASC Ship Core] CAP shield system not available")
        end

    elseif command == "cap_deactivate_shields" then
        if HYPERDRIVE.CAP.Shields then
            local success, message = HYPERDRIVE.CAP.Shields.Deactivate(self, self.ship, "manual")
            if success then
                ply:ChatPrint("[ASC Ship Core] CAP shields deactivated: " .. (message or "Success"))
            else
                ply:ChatPrint("[ASC Ship Core] CAP shield deactivation failed: " .. (message or "Unknown error"))
            end
        else
            ply:ChatPrint("[ASC Ship Core] CAP shield system not available")
        end

    elseif command == "cap_force_detection" then
        if ASC.CAP.Enhanced then
            ASC.CAP.Enhanced.DetectCAPEntities(true) -- Force update
            self:UpdateCAPStatus()
            ply:ChatPrint("[ASC Ship Core] CAP entity detection forced")
        else
            ply:ChatPrint("[ASC Ship Core] Enhanced CAP integration not available")
        end

    elseif command == "auto_weld" then
        -- Manual auto-weld trigger
        self:AutoWeldToNearestOwnedEntity()
        ply:ChatPrint("[ASC Ship Core] Manual auto-weld triggered")

    elseif command == "toggle_auto_weld" then
        -- Toggle auto-weld functionality
        if ply:IsAdmin() then
            self.Config.EnableAutoWeld = not self.Config.EnableAutoWeld
            ply:ChatPrint("[ASC Ship Core] Auto-weld " .. (self.Config.EnableAutoWeld and "enabled" or "disabled"))
        else
            ply:ChatPrint("[ASC Ship Core] Only admins can toggle auto-weld settings")
        end

    elseif command == "set_auto_weld_radius" and args[1] then
        -- Set auto-weld radius
        if ply:IsAdmin() then
            local radius = tonumber(args[1])
            if radius and radius > 0 and radius <= 2000 then
                self.Config.AutoWeldRadius = radius
                ply:ChatPrint("[ASC Ship Core] Auto-weld radius set to " .. radius)
            else
                ply:ChatPrint("[ASC Ship Core] Invalid radius (must be 1-2000)")
            end
        else
            ply:ChatPrint("[ASC Ship Core] Only admins can change auto-weld settings")
        end

    elseif command == "fix_invalid_status" then
        -- Fix invalid ship core status
        self:AttemptCoreValidationFix()
        ply:ChatPrint("[ASC Ship Core] Attempting to fix invalid status...")

    elseif command == "force_valid" then
        -- Force ship core to be valid (admin only)
        if ply:IsAdmin() then
            self:SetCoreValid(true)
            self:SetState(self.States.ACTIVE)
            self:SetStatusMessage("Forced to valid state by admin")
            ply:ChatPrint("[ASC Ship Core] Ship core forced to valid state")
        else
            ply:ChatPrint("[ASC Ship Core] Only admins can force valid state")
        end

    elseif command == "close_ui" then
        self:CloseUI(ply)
    end
end

-- Network message handlers
net.Receive("asc_ship_core_command", function(len, ply)
    local core = net.ReadEntity()
    local command = net.ReadString()
    local data = net.ReadTable()

    if not IsValid(core) or not IsValid(ply) then return end
    if core:GetClass() ~= "asc_ship_core" then return end

    core:HandleUICommand(ply, command, data)
end)

-- Network message handler for seat flight input
net.Receive("asc_seat_flight_input", function(len, ply)
    if not IsValid(ply) then return end

    local inputData = net.ReadTable()

    -- Store input data on player for ship core to read
    ply:SetNWTable("ASC_FlightInput", inputData)
end)

-- Ship name management
function ENT:SetShipName(name)
    self:SetNWString("ShipName", name or "Unnamed Ship")
end

function ENT:GetShipName()
    return self:GetNWString("ShipName", "Unnamed Ship")
end

function ENT:SaveShipName()
    local name = self:GetShipName()
    if name and name ~= "" then
        file.Write(self.shipNameFile, name)
    end
end

function ENT:LoadShipName()
    if file.Exists(self.shipNameFile, "DATA") then
        local name = file.Read(self.shipNameFile, "DATA")
        if name and name ~= "" then
            self:SetShipName(name)
        end
    end
end

-- Use function
function ENT:Use(activator, caller)
    if not IsValid(activator) or not activator:IsPlayer() then return end

    -- Open UI
    self:OpenUI(activator)
end

function ENT:OpenUI(ply)
    if not IsValid(ply) then return end

    -- Track active UI
    self.activeUIs[ply] = true

    -- Send UI data
    net.Start("asc_ship_core_open_ui")
    net.WriteEntity(self)
    net.WriteTable(self:GetUIData())
    net.Send(ply)

    print("[ASC Ship Core] UI opened for " .. ply:Nick())
end

function ENT:CloseUI(ply)
    if IsValid(ply) then
        self.activeUIs[ply] = nil
        net.Start("asc_ship_core_close_ui")
        net.Send(ply)
    end
end

function ENT:UpdateUI()
    if table.Count(self.activeUIs) == 0 then return end

    local data = self:GetUIData()

    for ply, _ in pairs(self.activeUIs) do
        if IsValid(ply) then
            net.Start("asc_ship_core_update_ui")
            net.WriteTable(data)
            net.Send(ply)
        else
            self.activeUIs[ply] = nil
        end
    end
end

function ENT:GetUIData()
    local data = {
        coreState = self:GetState(),
        coreStateName = self:GetStateName(),
        shipDetected = self:GetShipDetected(),
        shipType = self:GetShipType(),
        shipName = self:GetShipName(),
        hullIntegrity = self:GetHullIntegrity(),
        shieldStrength = self:GetShieldStrength(),
        hullSystemActive = self:GetHullSystemActive(),
        shieldSystemActive = self:GetShieldSystemActive(),
        statusMessage = self:GetStatusMessage(),
        -- ambientSoundMuted removed per user request
        modelInfo = self:GetModelInfo()
    }

    -- Add enhanced power management data
    if self.PowerManagement then
        data.powerManagement = {
            totalPower = self.PowerManagement.totalPower,
            availablePower = self.PowerManagement.availablePower,
            powerDistribution = self.PowerManagement.powerDistribution,
            emergencyMode = self.PowerManagement.emergencyMode,
            powerEfficiency = self.PowerManagement.powerEfficiency,
            heatGeneration = self.PowerManagement.heatGeneration
        }
    end

    -- Add thermal management data
    if self.ThermalManagement then
        data.thermalManagement = {
            coreTemperature = self.ThermalManagement.coreTemperature,
            maxTemperature = self.ThermalManagement.maxTemperature,
            overheating = self.ThermalManagement.overheating,
            coolingRate = self.ThermalManagement.coolingRate,
            thermalEfficiency = self.ThermalManagement.thermalEfficiency
        }
    end

    -- Add subsystem data
    if self.SubsystemManagement then
        data.subsystems = {}
        for name, subsystem in pairs(self.SubsystemManagement.subsystems) do
            data.subsystems[name] = {
                health = subsystem.health,
                efficiency = subsystem.efficiency,
                priority = subsystem.priority,
                critical = subsystem.critical
            }
        end
        data.autoRepair = self.SubsystemManagement.autoRepair
        data.repairRate = self.SubsystemManagement.repairRate
    end

    -- Add crew efficiency data
    if self.CrewEfficiency then
        data.crewEfficiency = {
            totalCrew = self.CrewEfficiency.totalCrew,
            overallEfficiency = self.CrewEfficiency.overallEfficiency,
            systemBonuses = self.CrewEfficiency.systemBonuses
        }
    end

    -- Add enhanced resource data
    if self.BasicResourceStorage then
        data.resources = {}
        for resourceType, resource in pairs(self.BasicResourceStorage) do
            data.resources[resourceType] = {
                amount = resource.amount,
                capacity = resource.capacity,
                percentage = (resource.amount / resource.capacity) * 100,
                regenRate = resource.regenRate,
                critical = resource.critical,
                priority = resource.priority
            }
        end
    end

    -- Add ship data if available
    if self.ship then
        data.entityCount = #self.ship:GetEntities()
        data.shipMass = self.ship.mass or 0
        data.shipCenter = self.ship:GetCenter()
        data.frontDirection = self.ship:GetFrontDirection()
    end

    return data
end

-- Wire integration
if WireLib then
    function ENT:TriggerInput(iname, value)
        -- Resource management inputs
        if iname == "AddEnergy" and value > 0 then
            self:AddResource("energy", value)
        elseif iname == "AddOxygen" and value > 0 then
            self:AddResource("oxygen", value)
        elseif iname == "AddCoolant" and value > 0 then
            self:AddResource("coolant", value)
        elseif iname == "AddFuel" and value > 0 then
            self:AddResource("fuel", value)
        elseif iname == "AddWater" and value > 0 then
            self:AddResource("water", value)
        elseif iname == "AddNitrogen" and value > 0 then
            self:AddResource("nitrogen", value)

        -- Resource system controls
        elseif iname == "DistributeResources" and value > 0 then
            if HYPERDRIVE.SB3Resources then
                HYPERDRIVE.SB3Resources.DistributeResources(self)
            end
        elseif iname == "CollectResources" and value > 0 then
            if HYPERDRIVE.SB3Resources then
                HYPERDRIVE.SB3Resources.CollectResources(self)
            end
        elseif iname == "BalanceResources" and value > 0 then
            if HYPERDRIVE.SB3Resources then
                HYPERDRIVE.SB3Resources.AutoBalanceResources(self)
            end
        elseif iname == "ToggleLifeSupport" then
            if HYPERDRIVE.SB3Resources then
                local storage = HYPERDRIVE.SB3Resources.GetCoreStorage(self)
                if storage then
                    storage.lifeSupportActive = value > 0
                end
            end

        -- System controls (ambient sound removed per user request)
        elseif iname == "Mute" then
            -- Ambient sound system removed
        elseif iname == "RepairHull" and value > 0 then
            if HYPERDRIVE.HullDamage and HYPERDRIVE.HullDamage.RepairHull then
                HYPERDRIVE.HullDamage.RepairHull(self, 25)
            end
        elseif iname == "ActivateShields" and value > 0 then
            if HYPERDRIVE.Shields and HYPERDRIVE.Shields.ActivateShields then
                HYPERDRIVE.Shields.ActivateShields(self)
            end
        elseif iname == "DeactivateShields" and value > 0 then
            if HYPERDRIVE.Shields and HYPERDRIVE.Shields.DeactivateShields then
                HYPERDRIVE.Shields.DeactivateShields(self)
            end
        elseif iname == "Recalculate" and value > 0 then
            self:Recalculate()
        elseif iname == "EmergencyResourceShutdown" and value > 0 then
            self:EmergencyResourceShutdown()
        end
    end

    function ENT:UpdateWireOutputs()
        if not WireLib then return end

        -- Core system outputs
        self:TriggerOutput("ShipDetected", self:GetShipDetected() and 1 or 0)
        self:TriggerOutput("ShipType", self:GetShipType())
        self:TriggerOutput("ShipName", self:GetShipName())
        self:TriggerOutput("CoreValid", self:GetCoreValid() and 1 or 0)
        self:TriggerOutput("CoreState", self:GetState())
        self:TriggerOutput("CoreStateName", self:GetStateName())
        self:TriggerOutput("StatusMessage", self:GetStatusMessage())

        -- Hull and shield outputs
        self:TriggerOutput("HullIntegrity", self:GetHullIntegrity())
        self:TriggerOutput("HullSystemActive", self:GetHullSystemActive() and 1 or 0)
        self:TriggerOutput("ShieldStrength", self:GetShieldStrength())
        self:TriggerOutput("ShieldSystemActive", self:GetShieldSystemActive() and 1 or 0)

        -- Ship information outputs
        self:TriggerOutput("ShipCenter", self:GetShipCenter())
        self:TriggerOutput("FrontDirection", self:GetFrontDirection())
        -- AmbientSoundMuted output removed per user request

        if self.ship then
            self:TriggerOutput("EntityCount", #self.ship:GetEntities())
            self:TriggerOutput("ShipMass", self.ship.mass or 0)
        else
            self:TriggerOutput("EntityCount", 0)
            self:TriggerOutput("ShipMass", 0)
        end

        -- Resource system outputs
        self:UpdateResourceWireOutputs()
    end
end

-- Additional functions for system integration
function ENT:UpdateCoreState()
    local currentState = self:GetState()
    local newState = currentState

    -- Determine state based on systems with improved logic
    if not self:GetShipDetected() then
        -- Try to detect ship before marking as inactive
        self:UpdateShipDetection()
        if not self:GetShipDetected() then
            newState = self.States.INACTIVE
        else
            newState = self.States.ACTIVE
        end
    elseif not self:GetCoreValid() then
        -- Don't immediately mark as invalid, try to fix first
        print("[ASC Ship Core] Core marked as invalid, attempting auto-fix...")
        self:AttemptCoreValidationFix()
        newState = self.States.ACTIVE -- Assume fix worked
    elseif self:GetHullIntegrity() < 25 then
        newState = self.States.EMERGENCY
    elseif self:GetHullIntegrity() < 50 then
        newState = self.States.CRITICAL
    else
        newState = self.States.ACTIVE
    end

    if newState ~= currentState then
        self:SetState(newState)
        self:SetStatusMessage(self:GetStateName())
        print("[ASC Ship Core] State changed from " .. (currentState or "unknown") .. " to " .. (newState or "unknown"))
    end
end

function ENT:UpdateResourceNetworkVars()
    -- Update resource-related network variables for UI
    if HYPERDRIVE.SB3Resources then
        local storage = HYPERDRIVE.SB3Resources.GetCoreStorage(self)
        if storage and storage.resources then
            -- Update individual resource levels
            self:SetNWFloat("EnergyLevel", storage.resources.energy or 0)
            self:SetNWFloat("OxygenLevel", storage.resources.oxygen or 0)
            self:SetNWFloat("CoolantLevel", storage.resources.coolant or 0)
            self:SetNWFloat("FuelLevel", storage.resources.fuel or 0)
            self:SetNWFloat("WaterLevel", storage.resources.water or 0)
            self:SetNWFloat("NitrogenLevel", storage.resources.nitrogen or 0)

            -- Update capacity information
            self:SetNWFloat("EnergyCapacity", storage.capacity.energy or 0)
            self:SetNWFloat("OxygenCapacity", storage.capacity.oxygen or 0)
            self:SetNWFloat("CoolantCapacity", storage.capacity.coolant or 0)
            self:SetNWFloat("FuelCapacity", storage.capacity.fuel or 0)
            self:SetNWFloat("WaterCapacity", storage.capacity.water or 0)
            self:SetNWFloat("NitrogenCapacity", storage.capacity.nitrogen or 0)

            -- Calculate percentages
            local energyPercent = storage.capacity.energy > 0 and (storage.resources.energy / storage.capacity.energy * 100) or 0
            local oxygenPercent = storage.capacity.oxygen > 0 and (storage.resources.oxygen / storage.capacity.oxygen * 100) or 0
            local fuelPercent = storage.capacity.fuel > 0 and (storage.resources.fuel / storage.capacity.fuel * 100) or 0

            self:SetNWFloat("EnergyPercent", energyPercent)
            self:SetNWFloat("OxygenPercent", oxygenPercent)
            self:SetNWFloat("FuelPercent", fuelPercent)

            -- Update system status
            self:SetNWBool("ResourceEmergency", storage.emergencyMode or false)
            self:SetNWBool("LifeSupportActive", storage.lifeSupportActive or false)
            self:SetNWInt("PlayersSupported", storage.playersSupported or 0)
            self:SetNWFloat("ShipSizeMultiplier", storage.sizeMultiplier or 1.0)

            -- Calculate total resources
            local totalAmount = (storage.resources.energy or 0) + (storage.resources.oxygen or 0) +
                               (storage.resources.coolant or 0) + (storage.resources.fuel or 0) +
                               (storage.resources.water or 0) + (storage.resources.nitrogen or 0)
            local totalCapacity = (storage.capacity.energy or 0) + (storage.capacity.oxygen or 0) +
                                 (storage.capacity.coolant or 0) + (storage.capacity.fuel or 0) +
                                 (storage.capacity.water or 0) + (storage.capacity.nitrogen or 0)

            self:SetNWFloat("TotalResourceAmount", totalAmount)
            self:SetNWFloat("TotalResourceCapacity", totalCapacity)

            -- Update activity status
            local lastActivity = storage.lastUpdate and (CurTime() - storage.lastUpdate < 5) and "Active" or "Inactive"
            self:SetNWString("LastResourceActivity", lastActivity)

            return
        end
    end

    -- Fallback to basic resource system
    self:UpdateBasicResourceNetworkVars()
end

function ENT:InitializeBasicResourceSystem()
    -- Initialize enhanced basic resource system when Spacebuild 3 is not available
    self:SetNWBool("ResourceSystemActive", true)
    self:SetNWBool("AutoProvisionEnabled", false)
    self:SetNWBool("WeldDetectionEnabled", false)
    -- Auto-weld system removed

    -- Initialize enhanced power management system
    self.PowerManagement = {
        totalPower = 1000,
        availablePower = 1000,
        powerDistribution = {
            weapons = { allocated = 200, priority = 1, efficiency = 1.0, active = true },
            shields = { allocated = 250, priority = 2, efficiency = 1.0, active = true },
            engines = { allocated = 150, priority = 3, efficiency = 1.0, active = true },
            lifesupport = { allocated = 100, priority = 4, efficiency = 1.0, active = true },
            sensors = { allocated = 50, priority = 5, efficiency = 1.0, active = true },
            communications = { allocated = 50, priority = 6, efficiency = 1.0, active = true },
            auxiliary = { allocated = 200, priority = 7, efficiency = 1.0, active = true }
        },
        emergencyMode = false,
        powerEfficiency = 1.0,
        heatGeneration = 0,
        coolingCapacity = 100
    }

    -- Initialize thermal management system
    self.ThermalManagement = {
        coreTemperature = 20, -- Celsius
        maxTemperature = 100,
        criticalTemperature = 150,
        coolingRate = 5,
        heatSources = {},
        thermalEfficiency = 1.0,
        overheating = false
    }

    -- Initialize subsystem management
    self.SubsystemManagement = {
        subsystems = {
            reactor = { health = 100, efficiency = 1.0, priority = 1, critical = true },
            powerGrid = { health = 100, efficiency = 1.0, priority = 2, critical = true },
            lifesupport = { health = 100, efficiency = 1.0, priority = 3, critical = true },
            navigation = { health = 100, efficiency = 1.0, priority = 4, critical = false },
            communications = { health = 100, efficiency = 1.0, priority = 5, critical = false },
            sensors = { health = 100, efficiency = 1.0, priority = 6, critical = false }
        },
        autoRepair = true,
        repairRate = 1.0,
        maintenanceSchedule = {}
    }

    -- Initialize basic resource storage with enhanced mechanics
    self.BasicResourceStorage = {
        energy = { amount = 1000, capacity = 1000, regenRate = 10, priority = 1, critical = true },
        oxygen = { amount = 500, capacity = 500, regenRate = 5, priority = 2, critical = true },
        coolant = { amount = 200, capacity = 200, regenRate = 2, priority = 3, critical = true },
        fuel = { amount = 300, capacity = 300, regenRate = 0, priority = 4, critical = false }, -- Fuel doesn't regenerate
        water = { amount = 150, capacity = 150, regenRate = 1, priority = 5, critical = false },
        nitrogen = { amount = 100, capacity = 100, regenRate = 1, priority = 6, critical = false }
    }

    -- Set initial network variables
    self:UpdateBasicResourceNetworkVars()

    -- Start basic resource regeneration timer
    self:StartBasicResourceRegeneration()

    self:SetNWString("LastResourceActivity", "Enhanced basic system initialized")
    print("[ASC Ship Core] Enhanced basic resource system initialized")
end

-- Ensure PowerManagement is initialized (safety function)
function ENT:EnsurePowerManagementInitialized()
    if not self.PowerManagement then
        print("[ASC Ship Core] PowerManagement not initialized, creating default...")
        self.PowerManagement = {
            totalPower = 1000,
            availablePower = 1000,
            powerDistribution = {
                weapons = { allocated = 200, priority = 1, efficiency = 1.0, active = true },
                shields = { allocated = 250, priority = 2, efficiency = 1.0, active = true },
                engines = { allocated = 150, priority = 3, efficiency = 1.0, active = true },
                lifesupport = { allocated = 100, priority = 4, efficiency = 1.0, active = true },
                sensors = { allocated = 50, priority = 5, efficiency = 1.0, active = true },
                communications = { allocated = 50, priority = 6, efficiency = 1.0, active = true },
                auxiliary = { allocated = 200, priority = 7, efficiency = 1.0, active = true }
            },
            emergencyMode = false,
            powerEfficiency = 1.0,
            heatGeneration = 0,
            coolingCapacity = 100
        }
    end
    return self.PowerManagement
end

-- Ensure ThermalManagement is initialized (safety function)
function ENT:EnsureThermalManagementInitialized()
    if not self.ThermalManagement then
        print("[ASC Ship Core] ThermalManagement not initialized, creating default...")
        self.ThermalManagement = {
            coreTemperature = 20, -- Celsius
            maxTemperature = 100,
            criticalTemperature = 150,
            coolingRate = 5,
            heatSources = {},
            thermalEfficiency = 1.0,
            overheating = false
        }
    end
    return self.ThermalManagement
end

-- Ensure SubsystemManagement is initialized (safety function)
function ENT:EnsureSubsystemManagementInitialized()
    if not self.SubsystemManagement then
        print("[ASC Ship Core] SubsystemManagement not initialized, creating default...")
        self.SubsystemManagement = {
            subsystems = {
                reactor = { health = 100, efficiency = 1.0, priority = 1, critical = true },
                powerGrid = { health = 100, efficiency = 1.0, priority = 2, critical = true },
                lifesupport = { health = 100, efficiency = 1.0, priority = 3, critical = true },
                navigation = { health = 100, efficiency = 1.0, priority = 4, critical = false },
                communications = { health = 100, efficiency = 1.0, priority = 5, critical = false },
                sensors = { health = 100, efficiency = 1.0, priority = 6, critical = false }
            },
            autoRepair = true,
            repairRate = 1.0,
            maintenanceSchedule = {}
        }
    end
    return self.SubsystemManagement
end

function ENT:UpdateBasicResourceNetworkVars()
    if not self.BasicResourceStorage then return end

    -- Update individual resource levels
    for resourceType, data in pairs(self.BasicResourceStorage) do
        local percent = data.capacity > 0 and (data.amount / data.capacity * 100) or 0

        self:SetNWFloat(string.upper(string.sub(resourceType, 1, 1)) .. string.sub(resourceType, 2) .. "Level", data.amount)
        self:SetNWFloat(string.upper(string.sub(resourceType, 1, 1)) .. string.sub(resourceType, 2) .. "Capacity", data.capacity)
        self:SetNWFloat(string.upper(string.sub(resourceType, 1, 1)) .. string.sub(resourceType, 2) .. "Percent", percent)
    end

    -- Calculate totals
    local totalAmount = 0
    local totalCapacity = 0
    local emergencyResources = 0

    for _, data in pairs(self.BasicResourceStorage) do
        totalAmount = totalAmount + data.amount
        totalCapacity = totalCapacity + data.capacity

        local percent = data.capacity > 0 and (data.amount / data.capacity * 100) or 0
        if percent < 25 then
            emergencyResources = emergencyResources + 1
        end
    end

    self:SetNWFloat("TotalResourceAmount", totalAmount)
    self:SetNWFloat("TotalResourceCapacity", totalCapacity)
    self:SetNWBool("ResourceEmergency", emergencyResources >= 2)
    self:SetNWBool("LifeSupportActive", self.BasicResourceStorage.oxygen.amount > 50)
    self:SetNWString("LastResourceActivity", "Basic system active - " .. os.date("%H:%M:%S"))
end

function ENT:StartBasicResourceRegeneration()
    -- Create timer for basic resource regeneration
    local timerName = "ASC_BasicResourceRegen_" .. self:EntIndex()

    timer.Create(timerName, 1, 0, function()
        if not IsValid(self) or not self.BasicResourceStorage then
            timer.Remove(timerName)
            return
        end

        self:UpdateBasicResourceRegeneration()
    end)
end

function ENT:UpdateBasicResourceRegeneration()
    if not self.BasicResourceStorage then return end

    local shipSize = self:GetShipSize()
    local sizeMultiplier = math.max(0.5, math.min(2.0, shipSize / 50)) -- Scale based on ship size

    -- Regenerate resources based on ship size (inverse scaling for balance)
    for resourceType, data in pairs(self.BasicResourceStorage) do
        if data.regenRate > 0 then
            -- Small ships regenerate faster but hold less
            -- Large ships hold more but regenerate slower
            local regenAmount = data.regenRate * (2.0 - sizeMultiplier) -- Inverse scaling
            local newAmount = math.min(data.capacity, data.amount + regenAmount)

            if newAmount ~= data.amount then
                data.amount = newAmount
                self:SetNWFloat(string.upper(string.sub(resourceType, 1, 1)) .. string.sub(resourceType, 2) .. "Level", data.amount)
            end
        end
    end

    -- Update network variables
    self:UpdateBasicResourceNetworkVars()

    -- Provide basic life support
    self:ProvideBasicLifeSupport()
end

function ENT:GetShipSize()
    if self.ship and self.ship.GetEntities then
        return #self.ship:GetEntities()
    end
    return 10 -- Default size
end

function ENT:ProvideBasicLifeSupport()
    if not self.BasicResourceStorage or self.BasicResourceStorage.oxygen.amount <= 0 then return end

    local corePos = self:GetPos()
    local lifeSupportRange = 1000 -- Basic life support range

    -- Find players within range
    for _, ply in ipairs(player.GetAll()) do
        if IsValid(ply) and ply:Alive() then
            local distance = corePos:Distance(ply:GetPos())
            if distance <= lifeSupportRange then
                -- Provide basic life support
                if ply:Health() < ply:GetMaxHealth() and ply:Health() > 0 then
                    local healAmount = math.min(1, ply:GetMaxHealth() - ply:Health())
                    ply:SetHealth(ply:Health() + healAmount)
                end

                -- Remove drowning effects
                if ply:WaterLevel() >= 3 then
                    ply:SetAir(ply:GetMaxAir())
                end

                -- Consume oxygen
                self.BasicResourceStorage.oxygen.amount = math.max(0, self.BasicResourceStorage.oxygen.amount - 0.1)

                -- Set life support status
                ply:SetNWBool("HasLifeSupport", true)
                ply:SetNWEntity("LifeSupportCore", self)
            else
                -- Remove life support status if out of range
                if ply:GetNWEntity("LifeSupportCore") == self then
                    ply:SetNWBool("HasLifeSupport", false)
                    ply:SetNWEntity("LifeSupportCore", NULL)
                end
            end
        end
    end
end

-- Resource management functions for both basic and SB3 systems
function ENT:GetResourceLevel(resourceType)
    if HYPERDRIVE.SB3Resources then
        local storage = HYPERDRIVE.SB3Resources.GetCoreStorage(self)
        if storage and storage.resources then
            return storage.resources[resourceType] or 0
        end
    elseif self.BasicResourceStorage then
        return self.BasicResourceStorage[resourceType] and self.BasicResourceStorage[resourceType].amount or 0
    end
    return 0
end

function ENT:GetResourceCapacity(resourceType)
    if HYPERDRIVE.SB3Resources then
        local storage = HYPERDRIVE.SB3Resources.GetCoreStorage(self)
        if storage and storage.capacity then
            return storage.capacity[resourceType] or 0
        end
    elseif self.BasicResourceStorage then
        return self.BasicResourceStorage[resourceType] and self.BasicResourceStorage[resourceType].capacity or 0
    end
    return 0
end

function ENT:GetResourcePercent(resourceType)
    local level = self:GetResourceLevel(resourceType)
    local capacity = self:GetResourceCapacity(resourceType)
    return capacity > 0 and (level / capacity * 100) or 0
end

function ENT:AddResource(resourceType, amount)
    if HYPERDRIVE.SB3Resources then
        return HYPERDRIVE.SB3Resources.AddResource(self, resourceType, amount)
    elseif self.BasicResourceStorage and self.BasicResourceStorage[resourceType] then
        local data = self.BasicResourceStorage[resourceType]
        local maxAdd = data.capacity - data.amount
        local actualAdd = math.min(amount, maxAdd)

        if actualAdd > 0 then
            data.amount = data.amount + actualAdd
            self:UpdateBasicResourceNetworkVars()
            return true, "Added " .. actualAdd .. " " .. resourceType
        end
        return false, "Storage full"
    end
    return false, "Resource system not available"
end

function ENT:RemoveResource(resourceType, amount)
    if HYPERDRIVE.SB3Resources then
        return HYPERDRIVE.SB3Resources.RemoveResource(self, resourceType, amount)
    elseif self.BasicResourceStorage and self.BasicResourceStorage[resourceType] then
        local data = self.BasicResourceStorage[resourceType]
        local actualRemove = math.min(amount, data.amount)

        if actualRemove > 0 then
            data.amount = data.amount - actualRemove
            self:UpdateBasicResourceNetworkVars()
            return true, "Removed " .. actualRemove .. " " .. resourceType
        end
        return false, "Insufficient resources"
    end
    return false, "Resource system not available"
end

function ENT:HasSufficientResources(requirements)
    for resourceType, amount in pairs(requirements) do
        if self:GetResourceLevel(resourceType) < amount then
            return false, "Insufficient " .. resourceType
        end
    end
    return true, "Sufficient resources"
end

function ENT:ConsumeResources(requirements)
    -- Check if we have sufficient resources first
    local sufficient, message = self:HasSufficientResources(requirements)
    if not sufficient then
        return false, message
    end

    -- Consume the resources
    for resourceType, amount in pairs(requirements) do
        local success, msg = self:RemoveResource(resourceType, amount)
        if not success then
            return false, "Failed to consume " .. resourceType .. ": " .. msg
        end
    end

    return true, "Resources consumed successfully"
end

function ENT:GetResourceStatus()
    local status = {
        systemActive = self:GetNWBool("ResourceSystemActive", false),
        emergencyMode = self:GetNWBool("ResourceEmergency", false),
        lifeSupportActive = self:GetNWBool("LifeSupportActive", false),
        totalAmount = self:GetNWFloat("TotalResourceAmount", 0),
        totalCapacity = self:GetNWFloat("TotalResourceCapacity", 0),
        resources = {}
    }

    local resourceTypes = {"energy", "oxygen", "coolant", "fuel", "water", "nitrogen"}
    for _, resourceType in ipairs(resourceTypes) do
        status.resources[resourceType] = {
            level = self:GetResourceLevel(resourceType),
            capacity = self:GetResourceCapacity(resourceType),
            percent = self:GetResourcePercent(resourceType)
        }
    end

    return status
end

-- Wire integration for resources
function ENT:UpdateResourceWireOutputs()
    if not WireLib then return end

    -- Output individual resource levels
    self:TriggerOutput("EnergyLevel", self:GetResourceLevel("energy"))
    self:TriggerOutput("OxygenLevel", self:GetResourceLevel("oxygen"))
    self:TriggerOutput("CoolantLevel", self:GetResourceLevel("coolant"))
    self:TriggerOutput("FuelLevel", self:GetResourceLevel("fuel"))
    self:TriggerOutput("WaterLevel", self:GetResourceLevel("water"))
    self:TriggerOutput("NitrogenLevel", self:GetResourceLevel("nitrogen"))

    -- Output resource percentages
    self:TriggerOutput("EnergyPercent", self:GetResourcePercent("energy"))
    self:TriggerOutput("OxygenPercent", self:GetResourcePercent("oxygen"))
    self:TriggerOutput("FuelPercent", self:GetResourcePercent("fuel"))

    -- Output system status
    self:TriggerOutput("ResourceEmergency", self:GetNWBool("ResourceEmergency", false) and 1 or 0)
    self:TriggerOutput("ResourceSystemActive", self:GetNWBool("ResourceSystemActive", false) and 1 or 0)
    self:TriggerOutput("LifeSupportActive", self:GetNWBool("LifeSupportActive", false) and 1 or 0)
    self:TriggerOutput("TotalResourceCapacity", self:GetNWFloat("TotalResourceCapacity", 0))
    self:TriggerOutput("TotalResourceAmount", self:GetNWFloat("TotalResourceAmount", 0))

    -- Output activity status
    self:TriggerOutput("LastResourceActivity", self:GetNWString("LastResourceActivity", "Unknown"))
end

-- Functions expected by other systems
function ENT:AddShield(shieldEntity)
    if not self.LinkedShields then
        self.LinkedShields = {}
    end
    table.insert(self.LinkedShields, shieldEntity)
    print("[ASC Ship Core] Shield generator linked: " .. tostring(shieldEntity))
end

function ENT:AddWeapon(weaponEntity)
    if not self.LinkedWeapons then
        self.LinkedWeapons = {}
    end
    table.insert(self.LinkedWeapons, weaponEntity)
    print("[ASC Ship Core] Weapon linked: " .. tostring(weaponEntity))
end

function ENT:AddComponent(componentEntity)
    if not self.LinkedComponents then
        self.LinkedComponents = {}
    end
    table.insert(self.LinkedComponents, componentEntity)
    print("[ASC Ship Core] Component linked: " .. tostring(componentEntity))
end

function ENT:AutoLinkComponents()
    -- Auto-link nearby components
    local nearbyEnts = ents.FindInSphere(self:GetPos(), 2000)
    local linkedCount = 0

    for _, ent in ipairs(nearbyEnts) do
        if IsValid(ent) and ent ~= self then
            local class = ent:GetClass()

            -- Link weapons
            if string.find(class, "weapon_") or string.find(class, "hyperdrive_") or
               string.find(class, "railgun") or string.find(class, "cannon") or
               string.find(class, "turret") or string.find(class, "launcher") then
                self:AddWeapon(ent)
                if ent.SetShipCore then
                    ent:SetShipCore(self)
                end
                linkedCount = linkedCount + 1

            -- Link shields
            elseif string.find(class, "shield") then
                self:AddShield(ent)
                if ent.SetShipCore then
                    ent:SetShipCore(self)
                end
                linkedCount = linkedCount + 1

            -- Link other components
            elseif string.find(class, "asc_") or string.find(class, "hyperdrive_") then
                self:AddComponent(ent)
                if ent.SetShipCore then
                    ent:SetShipCore(self)
                end
                linkedCount = linkedCount + 1
            end
        end
    end

    print("[ASC Ship Core] Auto-linked " .. linkedCount .. " components")
    return linkedCount
end

function ENT:Recalculate()
    -- Force recalculation of all systems
    self:UpdateShipDetection()
    self:UpdateSystems()
    self:AutoLinkComponents()
    print("[ASC Ship Core] Systems recalculated")
end

-- Handle when entities are welded to this ship core
function ENT:OnEntityWelded(entity)
    if not IsValid(entity) then return end

    print("[ASC Ship Core] Entity welded to ship: " .. entity:GetClass())

    -- Add to ship detection
    if self.ship then
        table.insert(self.ship.entities, entity)
    end

    -- Auto-link if it's an ASC component
    local class = entity:GetClass()
    if string.find(class, "asc_") or string.find(class, "hyperdrive_") then
        self:AddComponent(entity)
        if entity.SetShipCore then
            entity:SetShipCore(self)
        end
        print("[ASC Ship Core] Auto-linked component: " .. class)
    end

    -- Provide resources if resource system is active
    if self:GetNWBool("ResourceSystemActive", false) and self:GetNWBool("AutoProvisionEnabled", false) then
        timer.Simple(0.5, function()
            if IsValid(self) and IsValid(entity) then
                self:ProvideResourcesTo(entity)
            end
        end)
    end

    -- Update ship detection
    timer.Simple(1, function()
        if IsValid(self) then
            self:UpdateShipDetection()
        end
    end)
end

-- Provide resources to a newly welded entity
function ENT:ProvideResourcesTo(entity)
    if not IsValid(entity) then return end

    -- Check if entity has resource capacity
    local hasResources = false
    local resourceTypes = {"energy", "oxygen", "coolant", "fuel", "water", "nitrogen"}

    for _, resourceType in ipairs(resourceTypes) do
        local capacityMethod = "Get" .. string.upper(string.sub(resourceType, 1, 1)) .. string.sub(resourceType, 2) .. "Capacity"
        local setMethod = "Set" .. string.upper(string.sub(resourceType, 1, 1)) .. string.sub(resourceType, 2)

        if entity[capacityMethod] and entity[setMethod] then
            local capacity = entity[capacityMethod](entity)
            if capacity and capacity > 0 then
                local amount = math.min(capacity * 0.5, 100) -- Provide 50% or 100 units, whichever is smaller
                entity[setMethod](entity, amount)
                hasResources = true
                print("[ASC Ship Core] Provided " .. amount .. " " .. resourceType .. " to " .. entity:GetClass())
            end
        end
    end

    if hasResources then
        self:SetNWString("LastResourceActivity", "Provided resources to " .. entity:GetClass())
    end
end

-- Initialize welding detection system
function ENT:InitializeWeldingDetection()
    -- Set up constraint detection hook for this specific ship core
    local hookName = "ASC_ShipCore_WeldDetection_" .. self:EntIndex()

    hook.Add("OnEntityCreated", hookName, function(ent)
        if not IsValid(ent) or not IsValid(self) then
            hook.Remove("OnEntityCreated", hookName)
            return
        end

        -- Check if this is a constraint
        if ent:GetClass() == "phys_constraint" or string.find(ent:GetClass(), "constraint") then
            timer.Simple(0.1, function()
                if IsValid(ent) and IsValid(self) then
                    self:CheckConstraintForWelding(ent)
                end
            end)
        end
    end)

    print("[ASC Ship Core] Welding detection system initialized")
end

-- Check if a constraint involves this ship core
function ENT:CheckConstraintForWelding(constraint)
    if not IsValid(constraint) then return end

    -- Get the entities involved in the constraint
    local ent1 = constraint.Ent1
    local ent2 = constraint.Ent2

    if not IsValid(ent1) or not IsValid(ent2) then return end

    -- Check if one of the entities is this ship core
    if ent1 == self then
        self:OnEntityWelded(ent2)
    elseif ent2 == self then
        self:OnEntityWelded(ent1)
    end
end

-- Auto-weld system implementation
function ENT:AutoWeldToNearestOwnedEntity()
    if not IsValid(self) then return end

    -- Check if auto-weld is enabled
    if self.Config and not self.Config.EnableAutoWeld then
        print("[ASC Ship Core] Auto-weld disabled in configuration")
        return
    end

    -- Get ship core owner
    local owner = self:CPPIGetOwner()
    if not IsValid(owner) then
        print("[ASC Ship Core] Auto-weld: No valid owner found")
        return
    end

    print("[ASC Ship Core] Auto-weld: Searching for nearest entity owned by " .. owner:Name())

    -- Find entities in radius
    local searchRadius = (self.Config and self.Config.AutoWeldRadius) or 500
    local nearbyEntities = ents.FindInSphere(self:GetPos(), searchRadius)

    local nearestEntity = nil
    local nearestDistance = math.huge

    for _, ent in ipairs(nearbyEntities) do
        if self:IsValidAutoWeldTarget(ent, owner) then
            local distance = self:GetPos():Distance(ent:GetPos())
            if distance < nearestDistance then
                nearestDistance = distance
                nearestEntity = ent
            end
        end
    end

    -- Weld to nearest valid entity
    if IsValid(nearestEntity) then
        self:WeldToEntity(nearestEntity)
    else
        print("[ASC Ship Core] Auto-weld: No valid entities found to weld to")
    end
end

-- Check if entity is valid for auto-welding
function ENT:IsValidAutoWeldTarget(entity, owner)
    if not IsValid(entity) or not IsValid(owner) then return false end

    -- Don't weld to self
    if entity == self then return false end

    -- Check ownership
    local entityOwner = nil
    if entity.CPPIGetOwner then
        entityOwner = entity:CPPIGetOwner()
    elseif entity.GetOwner then
        entityOwner = entity:GetOwner()
    end

    if not IsValid(entityOwner) or entityOwner ~= owner then return false end

    -- Don't weld to other ship cores
    if entity:GetClass() == "asc_ship_core" or entity:GetClass() == "ship_core" then return false end

    -- Don't weld to players
    if entity:IsPlayer() then return false end

    -- Don't weld to NPCs
    if entity:IsNPC() then return false end

    -- Don't weld to constraints
    if string.find(entity:GetClass(), "constraint") then return false end

    -- Don't weld to effects or temporary entities
    if entity:GetClass() == "env_sprite" or entity:GetClass() == "info_particle_system" then return false end

    -- Check if entity has physics
    local phys = entity:GetPhysicsObject()
    if not IsValid(phys) then return false end

    -- Check if already welded to this entity
    if self:IsAlreadyWeldedTo(entity) then return false end

    print("[ASC Ship Core] Auto-weld: Found valid target: " .. entity:GetClass() .. " at distance " .. math.Round(self:GetPos():Distance(entity:GetPos())))
    return true
end

-- Check if already welded to entity
function ENT:IsAlreadyWeldedTo(entity)
    if not IsValid(entity) then return false end

    local constraints = constraint.GetAllConstrainedEntities(self)
    if not constraints then return false end

    for constrainedEnt, _ in pairs(constraints) do
        if constrainedEnt == entity then
            return true
        end
    end

    return false
end

-- Weld ship core to target entity
function ENT:WeldToEntity(targetEntity)
    if not IsValid(targetEntity) then return false end

    local selfPhys = self:GetPhysicsObject()
    local targetPhys = targetEntity:GetPhysicsObject()

    if not IsValid(selfPhys) or not IsValid(targetPhys) then
        print("[ASC Ship Core] Auto-weld: Invalid physics objects")
        return false
    end

    -- Create weld constraint
    local weldConstraint = constraint.Weld(self, targetEntity, 0, 0, 0, true, false)

    if IsValid(weldConstraint) then
        print("[ASC Ship Core] Auto-weld: Successfully welded to " .. targetEntity:GetClass())

        -- Trigger welded event
        self:OnEntityWelded(targetEntity)

        -- Notify owner
        local owner = self:CPPIGetOwner()
        if IsValid(owner) then
            owner:ChatPrint("[ASC Ship Core] Auto-welded to nearest entity: " .. targetEntity:GetClass())
        end

        return true
    else
        print("[ASC Ship Core] Auto-weld: Failed to create weld constraint")
        return false
    end
end

-- Attempt to fix core validation issues
function ENT:AttemptCoreValidationFix()
    print("[ASC Ship Core] Attempting to fix core validation issues...")

    -- Force core to be valid
    self:SetCoreValid(true)

    -- Reset state to active
    self:SetState(self.States.ACTIVE)

    -- Update status message
    self:SetStatusMessage("Core validation fixed - ship operational")

    -- Force ship detection refresh
    timer.Simple(0.5, function()
        if IsValid(self) then
            self:UpdateShipDetection()
        end
    end)

    -- Notify owner
    local owner = self:CPPIGetOwner()
    if IsValid(owner) then
        owner:ChatPrint("[ASC Ship Core] Core validation issues resolved automatically")
    end

    print("[ASC Ship Core] Core validation fix completed")
end

-- Enhanced Flight System Implementation v6.2.1
function ENT:InitializeEnhancedFlightSystem()
    print("[ASC Ship Core] Initializing Enhanced Flight System v6.2.1...")

    -- Initialize flight state
    self.FlightSystem = {
        -- Core flight properties
        active = false,
        mode = "MANUAL", -- MANUAL, AUTOPILOT, FORMATION, DOCKING

        -- 6DOF Movement vectors
        velocity = Vector(0, 0, 0),
        angularVelocity = Angle(0, 0, 0),
        acceleration = Vector(0, 0, 0),
        angularAcceleration = Angle(0, 0, 0),

        -- Thruster control (6DOF)
        thrusters = {
            forward = 0,    -- Z+
            backward = 0,   -- Z-
            left = 0,       -- Y-
            right = 0,      -- Y+
            up = 0,         -- X+
            down = 0,       -- X-
            pitchUp = 0,    -- Pitch+
            pitchDown = 0,  -- Pitch-
            yawLeft = 0,    -- Yaw-
            yawRight = 0,   -- Yaw+
            rollLeft = 0,   -- Roll-
            rollRight = 0   -- Roll+
        },

        -- Flight characteristics
        mass = 1000,
        maxThrust = 5000,
        maxAngularThrust = 100,
        maxSpeed = 2000,
        maxAngularSpeed = 45,

        -- Inertial dampening
        inertialDampening = true,
        dampeningStrength = 0.95,
        angularDampening = 0.90,

        -- Stability systems
        stabilityAssist = true,
        autoLevel = false,
        collisionAvoidance = true,
        proximityWarning = true,

        -- Autopilot
        autopilot = {
            active = false,
            target = nil,
            waypoints = {},
            currentWaypoint = 1,
            speed = 500,
            accuracy = 50,
            avoidanceRadius = 200
        },

        -- Formation flying
        formation = {
            active = false,
            leader = nil,
            position = Vector(0, 0, 0),
            offset = Vector(100, 0, 0),
            followers = {}
        },

        -- Docking system
        docking = {
            active = false,
            target = nil,
            phase = "APPROACH", -- APPROACH, ALIGN, DOCK, DOCKED
            distance = 0,
            alignment = 0
        },

        -- Cloaking system
        cloaking = {
            active = false,
            enabled = true,
            energyCost = 50, -- Energy per second while cloaked
            chargeTime = 3.0, -- Time to fully cloak/decloak
            currentCharge = 0.0, -- 0.0 = visible, 1.0 = fully cloaked
            efficiency = 1.0, -- Cloaking efficiency (0.0 - 1.0)
            detectionRange = 500, -- Range at which cloaked ships can be detected
            movementPenalty = 0.7, -- Speed reduction while cloaked (0.3 = 30% speed)
            lastToggleTime = 0,
            cooldownTime = 5.0, -- Cooldown after decloaking
            disruptionTime = 0, -- Time remaining for cloak disruption
            maxDisruptionTime = 10.0
        },

        -- Performance tracking
        stats = {
            distanceTraveled = 0,
            energyConsumed = 0,
            flightTime = 0,
            maxSpeedReached = 0,
            waypointsCompleted = 0
        },

        -- Safety systems
        safety = {
            emergencyBraking = false,
            collisionWarning = false,
            proximityAlert = false,
            systemFailure = false,
            lastCollisionCheck = 0
        },

        -- Seat control system
        seatControl = {
            enabled = true,
            pilot = nil,
            pilotSeat = nil,
            controlRange = 2000,
            autoActivateOnSit = true,
            autoDeactivateOnExit = true,
            keyBindings = {
                forward = KEY_W,
                backward = KEY_S,
                left = KEY_A,
                right = KEY_D,
                up = KEY_SPACE,
                down = KEY_LCONTROL,
                boost = KEY_LSHIFT,
                brake = KEY_X,
                autopilot = KEY_R,
                autoLevel = KEY_T
            },
            sensitivity = {
                movement = 1.0,
                rotation = 0.5,
                boost = 2.0
            },
            lastInputTime = 0,
            inputBuffer = {}
        }
    }

    -- Calculate ship mass based on entities
    self:CalculateShipMass()

    -- Set up flight system networking
    self:SetupFlightNetworking()

    -- Initialize flight controls
    self:InitializeFlightControls()

    -- Initialize seat control system
    self:InitializeSeatControl()

    -- Initialize cloaking system
    self:InitializeCloakingSystem()

    -- Start flight system update loop
    self:StartFlightSystemUpdates()

    print("[ASC Ship Core] Enhanced Flight System initialized successfully")
    self:SetNWBool("EnhancedFlightActive", true)
end

-- Calculate ship mass for realistic physics
function ENT:CalculateShipMass()
    if not self.FlightSystem then return end

    local totalMass = 100 -- Base ship core mass
    local entityCount = 0

    -- Get all ship entities
    local shipEntities = constraint.GetAllConstrainedEntities(self)
    if shipEntities then
        for ent, _ in pairs(shipEntities) do
            if IsValid(ent) and ent ~= self then
                entityCount = entityCount + 1
                local phys = ent:GetPhysicsObject()
                if IsValid(phys) then
                    totalMass = totalMass + phys:GetMass()
                else
                    totalMass = totalMass + 50 -- Default mass for non-physics entities
                end
            end
        end
    end

    -- Apply mass scaling for gameplay balance
    self.FlightSystem.mass = math.max(500, totalMass * 0.5)

    -- Adjust flight characteristics based on mass
    local massRatio = self.FlightSystem.mass / 1000
    self.FlightSystem.maxThrust = 5000 / math.sqrt(massRatio)
    self.FlightSystem.maxSpeed = 2000 / math.sqrt(massRatio)

    print("[ASC Flight] Ship mass calculated: " .. math.Round(self.FlightSystem.mass) .. " kg (" .. entityCount .. " entities)")
end

-- Set up flight system networking
function ENT:SetupFlightNetworking()
    if not self.FlightSystem then return end

    -- Network flight status
    self:SetNWBool("FlightActive", false)
    self:SetNWString("FlightMode", "MANUAL")
    self:SetNWFloat("FlightSpeed", 0)
    self:SetNWFloat("FlightMass", self.FlightSystem.mass)
    self:SetNWFloat("FlightMaxSpeed", self.FlightSystem.maxSpeed)
    self:SetNWFloat("FlightMaxThrust", self.FlightSystem.maxThrust)

    -- Network thruster states
    self:SetNWFloat("ThrusterForward", 0)
    self:SetNWFloat("ThrusterBackward", 0)
    self:SetNWFloat("ThrusterLeft", 0)
    self:SetNWFloat("ThrusterRight", 0)
    self:SetNWFloat("ThrusterUp", 0)
    self:SetNWFloat("ThrusterDown", 0)

    -- Network autopilot status
    self:SetNWBool("AutopilotActive", false)
    self:SetNWVector("AutopilotTarget", Vector(0, 0, 0))
    self:SetNWInt("WaypointCount", 0)
    self:SetNWInt("CurrentWaypoint", 0)

    -- Network safety systems
    self:SetNWBool("InertialDampening", true)
    self:SetNWBool("StabilityAssist", true)
    self:SetNWBool("CollisionAvoidance", true)
    self:SetNWBool("ProximityWarning", false)
    self:SetNWBool("EmergencyBraking", false)

    print("[ASC Flight] Flight networking initialized")
end

-- Initialize flight controls
function ENT:InitializeFlightControls()
    if not self.FlightSystem then return end

    -- Set up wire inputs for flight control
    self.Inputs = Wire_CreateInputs(self, {
        -- 6DOF movement controls
        "ThrustForward", "ThrustBackward", "ThrustLeft", "ThrustRight", "ThrustUp", "ThrustDown",
        "PitchUp", "PitchDown", "YawLeft", "YawRight", "RollLeft", "RollRight",

        -- Flight mode controls
        "FlightActive", "FlightMode", "InertialDampening", "StabilityAssist",

        -- Autopilot controls
        "AutopilotActive", "AutopilotTargetX", "AutopilotTargetY", "AutopilotTargetZ",
        "AutopilotSpeed", "AddWaypoint", "ClearWaypoints",

        -- Safety systems
        "CollisionAvoidance", "EmergencyBrake", "AutoLevel"
    })

    -- Set up wire outputs for flight status
    self.Outputs = Wire_CreateOutputs(self, {
        -- Flight status
        "FlightActive", "FlightSpeed", "FlightMass", "FlightMode",

        -- Position and velocity
        "VelocityX", "VelocityY", "VelocityZ", "Speed",
        "PositionX", "PositionY", "PositionZ",

        -- Autopilot status
        "AutopilotActive", "DistanceToTarget", "WaypointsRemaining",

        -- Safety status
        "CollisionWarning", "ProximityAlert", "EmergencyBraking"
    })

    print("[ASC Flight] Flight controls initialized")
end

-- Start flight system update loop
function ENT:StartFlightSystemUpdates()
    if not self.FlightSystem then return end

    local updateTimer = "ASC_FlightUpdate_" .. self:EntIndex()
    timer.Create(updateTimer, 0.05, 0, function() -- 20 FPS update rate
        if IsValid(self) and self.FlightSystem then
            self:UpdateFlightSystem()
        else
            timer.Remove(updateTimer)
        end
    end)

    print("[ASC Flight] Flight update loop started (20 FPS)")
end

-- Initialize cloaking system
function ENT:InitializeCloakingSystem()
    if not self.FlightSystem then return end

    -- Set up cloaking networking
    self:SetNWBool("CloakingActive", false)
    self:SetNWFloat("CloakingCharge", 0.0)
    self:SetNWBool("CloakingEnabled", true)
    self:SetNWFloat("CloakingEfficiency", 1.0)
    self:SetNWFloat("CloakingCooldown", 0.0)
    self:SetNWBool("CloakingDisrupted", false)

    -- Add cloaking wire inputs/outputs
    if self.Inputs then
        -- Add cloaking inputs to existing wire inputs
        local cloakInputs = {
            "CloakingActivate", "CloakingDeactivate", "CloakingToggle",
            "CloakingEfficiency", "CloakingEnabled"
        }

        for _, input in ipairs(cloakInputs) do
            if not self.Inputs[input] then
                self.Inputs[input] = {Value = 0}
            end
        end
    end

    if self.Outputs then
        -- Add cloaking outputs to existing wire outputs
        local cloakOutputs = {
            "CloakingActive", "CloakingCharge", "CloakingEfficiency",
            "CloakingCooldown", "CloakingDisrupted", "CloakingEnergyUsage"
        }

        for _, output in ipairs(cloakOutputs) do
            if not self.Outputs[output] then
                self.Outputs[output] = {Value = 0}
            end
        end
    end

    print("[ASC Cloaking] Cloaking system initialized")
end

-- Initialize built-in hull system
function ENT:InitializeBuiltInHullSystem()
    print("[ASC Hull] Initializing built-in hull damage system...")

    -- Initialize hull system
    self.HullSystem = {
        enabled = true,
        maxHull = 1000,
        currentHull = 1000,
        damageHistory = {},
        repairRate = 5, -- Hull points per second
        autoRepair = true,
        criticalThreshold = 0.25, -- 25% hull
        emergencyThreshold = 0.10, -- 10% hull
        breaches = {},
        systemFailures = {},
        armorRating = 1.0,
        damageResistance = {
            kinetic = 0.1,
            energy = 0.15,
            explosive = 0.05,
            thermal = 0.2
        },
        -- Enhanced damage tracking
        totalDamageReceived = 0,
        lastDamageTime = 0,
        damagePerSecond = 0,
        criticalMode = false,
        emergencyMode = false,
        repairProgress = 0,
        autoRepairActive = false,
        damageEffects = {},
        structuralIntegrity = 1.0,
        hullSections = {
            bow = { health = 100, maxHealth = 100, critical = false },
            stern = { health = 100, maxHealth = 100, critical = false },
            port = { health = 100, maxHealth = 100, critical = false },
            starboard = { health = 100, maxHealth = 100, critical = false },
            dorsal = { health = 100, maxHealth = 100, critical = false },
            ventral = { health = 100, maxHealth = 100, critical = false }
        }
    }

    -- Set up hull networking
    self:SetNWFloat("HullIntegrity", 1.0)
    self:SetNWFloat("HullMaxHealth", self.HullSystem.maxHull)
    self:SetNWFloat("HullCurrentHealth", self.HullSystem.currentHull)
    self:SetNWBool("HullCriticalMode", false)
    self:SetNWBool("HullEmergencyMode", false)
    self:SetNWBool("HullAutoRepair", self.HullSystem.autoRepair)
    self:SetNWFloat("HullRepairRate", self.HullSystem.repairRate)
    self:SetNWFloat("HullArmorRating", self.HullSystem.armorRating)

    -- Set up hull wire inputs/outputs
    if self.Inputs then
        local hullInputs = {
            "HullRepair", "HullAutoRepair", "HullArmorRating"
        }

        for _, input in ipairs(hullInputs) do
            if not self.Inputs[input] then
                self.Inputs[input] = {Value = 0}
            end
        end
    end

    if self.Outputs then
        local hullOutputs = {
            "HullIntegrity", "HullHealth", "HullMaxHealth", "HullCritical",
            "HullEmergency", "HullRepairActive", "HullDamageRate"
        }

        for _, output in ipairs(hullOutputs) do
            if not self.Outputs[output] then
                self.Outputs[output] = {Value = 0}
            end
        end
    end

    -- Start hull system update loop
    local hullTimer = "ASC_HullUpdate_" .. self:EntIndex()
    timer.Create(hullTimer, 1.0, 0, function() -- 1 FPS update rate
        if IsValid(self) and self.HullSystem then
            self:UpdateBuiltInHullSystem()
        else
            timer.Remove(hullTimer)
        end
    end)

    print("[ASC Hull] Built-in hull system initialized")
end

-- Update built-in hull system
function ENT:UpdateBuiltInHullSystem()
    local hull = self.HullSystem
    if not hull or not hull.enabled then return end

    local deltaTime = 1.0 -- 1 second update interval
    local currentTime = CurTime()

    -- Auto-repair system
    if hull.autoRepair and hull.currentHull < hull.maxHull then
        hull.autoRepairActive = true
        hull.repairProgress = hull.repairProgress + hull.repairRate * deltaTime

        if hull.repairProgress >= 1.0 then
            local repairAmount = math.floor(hull.repairProgress)
            hull.currentHull = math.min(hull.maxHull, hull.currentHull + repairAmount)
            hull.repairProgress = hull.repairProgress - repairAmount
        end
    else
        hull.autoRepairActive = false
        hull.repairProgress = 0
    end

    -- Calculate hull integrity
    local integrity = hull.currentHull / hull.maxHull
    hull.structuralIntegrity = integrity

    -- Update critical/emergency modes
    local wasCritical = hull.criticalMode
    local wasEmergency = hull.emergencyMode

    hull.emergencyMode = integrity <= hull.emergencyThreshold
    hull.criticalMode = integrity <= hull.criticalThreshold and not hull.emergencyMode

    -- Mode change notifications
    if hull.emergencyMode and not wasEmergency then
        print("[ASC Hull] HULL EMERGENCY - " .. math.Round(integrity * 100) .. "% integrity")
        self:OnHullEmergency()
    elseif hull.criticalMode and not wasCritical then
        print("[ASC Hull] HULL CRITICAL - " .. math.Round(integrity * 100) .. "% integrity")
        self:OnHullCritical()
    elseif not hull.criticalMode and not hull.emergencyMode and (wasCritical or wasEmergency) then
        print("[ASC Hull] Hull integrity restored - " .. math.Round(integrity * 100) .. "% integrity")
        self:OnHullRestored()
    end

    -- Calculate damage rate
    if currentTime - hull.lastDamageTime < 5.0 then
        hull.damagePerSecond = hull.totalDamageReceived / math.max(1, currentTime - hull.lastDamageTime)
    else
        hull.damagePerSecond = 0
    end

    -- Update hull sections
    self:UpdateHullSections()

    -- Update networking
    self:UpdateHullNetworking()

    -- Update wire outputs
    self:UpdateHullWireOutputs()
end

-- Update hull sections
function ENT:UpdateHullSections()
    local hull = self.HullSystem
    if not hull then return end

    local integrity = hull.currentHull / hull.maxHull

    -- Distribute damage across hull sections
    for sectionName, section in pairs(hull.hullSections) do
        -- Calculate section health based on overall integrity
        local targetHealth = integrity * section.maxHealth
        section.health = math.min(section.maxHealth, math.max(0, targetHealth))

        -- Mark sections as critical if below 25%
        section.critical = (section.health / section.maxHealth) < 0.25
    end
end

-- Update hull networking
function ENT:UpdateHullNetworking()
    local hull = self.HullSystem
    if not hull then return end

    local integrity = hull.currentHull / hull.maxHull

    self:SetNWFloat("HullIntegrity", integrity)
    self:SetNWFloat("HullCurrentHealth", hull.currentHull)
    self:SetNWBool("HullCriticalMode", hull.criticalMode)
    self:SetNWBool("HullEmergencyMode", hull.emergencyMode)
    self:SetNWBool("HullAutoRepairActive", hull.autoRepairActive)
    self:SetNWFloat("HullDamageRate", hull.damagePerSecond)
    self:SetNWFloat("HullStructuralIntegrity", hull.structuralIntegrity)
end

-- Update hull wire outputs
function ENT:UpdateHullWireOutputs()
    local hull = self.HullSystem
    if not hull or not self.Outputs then return end

    local integrity = hull.currentHull / hull.maxHull

    Wire_TriggerOutput(self, "HullIntegrity", integrity)
    Wire_TriggerOutput(self, "HullHealth", hull.currentHull)
    Wire_TriggerOutput(self, "HullMaxHealth", hull.maxHull)
    Wire_TriggerOutput(self, "HullCritical", hull.criticalMode and 1 or 0)
    Wire_TriggerOutput(self, "HullEmergency", hull.emergencyMode and 1 or 0)
    Wire_TriggerOutput(self, "HullRepairActive", hull.autoRepairActive and 1 or 0)
    Wire_TriggerOutput(self, "HullDamageRate", hull.damagePerSecond)
end

-- Hull event handlers
function ENT:OnHullCritical()
    -- Reduce system efficiency
    if self.FlightSystem then
        self.FlightSystem.efficiency = 0.7
    end

    if self.ShieldSystem then
        self.ShieldSystem.efficiency = 0.8
    end

    -- Create warning effects
    self:CreateHullWarningEffects("critical")

    -- Notify players
    self:NotifyPlayersOnShip("WARNING: Hull integrity critical!")
end

function ENT:OnHullEmergency()
    -- Severely reduce system efficiency
    if self.FlightSystem then
        self.FlightSystem.efficiency = 0.4
        -- Force decloak if active
        if self.FlightSystem.cloaking and self.FlightSystem.cloaking.active then
            self:DisruptCloaking("Hull emergency")
        end
    end

    if self.ShieldSystem then
        self.ShieldSystem.efficiency = 0.5
    end

    -- Create emergency effects
    self:CreateHullWarningEffects("emergency")

    -- Notify players
    self:NotifyPlayersOnShip("EMERGENCY: Hull integrity critical! Immediate repair required!")
end

function ENT:OnHullRestored()
    -- Restore system efficiency
    if self.FlightSystem then
        self.FlightSystem.efficiency = 1.0
    end

    if self.ShieldSystem then
        self.ShieldSystem.efficiency = 1.0
    end

    -- Notify players
    self:NotifyPlayersOnShip("Hull integrity restored")
end

-- Create hull warning effects
function ENT:CreateHullWarningEffects(severity)
    local pos = self:GetPos()

    if severity == "critical" then
        -- Orange sparks
        local effectdata = EffectData()
        effectdata:SetOrigin(pos)
        effectdata:SetMagnitude(5)
        effectdata:SetScale(1)
        util.Effect("ElectricSpark", effectdata)

        self:EmitSound("ambient/energy/spark" .. math.random(1, 6) .. ".wav", 70, 100)

    elseif severity == "emergency" then
        -- Red sparks and smoke
        local effectdata = EffectData()
        effectdata:SetOrigin(pos)
        effectdata:SetMagnitude(10)
        effectdata:SetScale(2)
        util.Effect("ElectricSpark", effectdata)

        effectdata:SetOrigin(pos + Vector(0, 0, 20))
        effectdata:SetMagnitude(3)
        util.Effect("smoke_trail", effectdata)

        self:EmitSound("ambient/fire/fire_med_loop1.wav", 80, 120)
    end
end

-- Notify players on ship
function ENT:NotifyPlayersOnShip(message)
    if not self.ship or not self.ship.entities then return end

    local players = {}
    for _, ent in ipairs(self.ship.entities) do
        if IsValid(ent) and ent:IsPlayer() then
            table.insert(players, ent)
        elseif IsValid(ent) and ent:IsVehicle() then
            local driver = ent:GetDriver()
            if IsValid(driver) then
                table.insert(players, driver)
            end
        end
    end

    for _, ply in ipairs(players) do
        ply:ChatPrint("[ASC Hull] " .. message)
    end
end

-- Initialize combat system
function ENT:InitializeCombatSystem()
    print("[ASC Combat] Initializing combat system...")

    -- Initialize combat system
    self.CombatSystem = {
        enabled = true,
        weapons = {},
        defenses = {},
        targetingSystem = {
            enabled = true,
            range = 5000,
            targets = {},
            currentTarget = nil,
            autoTarget = false,
            friendlyFire = false
        },
        combatMode = "DEFENSIVE", -- DEFENSIVE, AGGRESSIVE, PASSIVE
        alertLevel = "GREEN", -- GREEN, YELLOW, RED
        lastCombatTime = 0,
        totalDamageDealt = 0,
        totalDamageReceived = 0,
        combatEfficiency = 1.0,
        tacticalAI = {
            enabled = false,
            behavior = "balanced", -- defensive, aggressive, balanced
            priority = "closest" -- closest, strongest, weakest
        }
    }

    -- Set up combat networking
    self:SetNWBool("CombatSystemActive", true)
    self:SetNWString("CombatMode", self.CombatSystem.combatMode)
    self:SetNWString("AlertLevel", self.CombatSystem.alertLevel)
    self:SetNWBool("TargetingActive", self.CombatSystem.targetingSystem.enabled)
    self:SetNWFloat("TargetingRange", self.CombatSystem.targetingSystem.range)
    self:SetNWBool("AutoTargeting", self.CombatSystem.targetingSystem.autoTarget)
    self:SetNWFloat("CombatEfficiency", self.CombatSystem.combatEfficiency)

    -- Set up combat wire inputs/outputs
    if self.Inputs then
        local combatInputs = {
            "CombatMode", "AlertLevel", "TargetingRange", "AutoTarget",
            "SetTarget [ENTITY]", "FireWeapons", "ActivateDefenses"
        }

        for _, input in ipairs(combatInputs) do
            if not self.Inputs[input] then
                self.Inputs[input] = {Value = 0}
            end
        end
    end

    if self.Outputs then
        local combatOutputs = {
            "CombatActive", "AlertLevel", "TargetCount", "CurrentTarget [ENTITY]",
            "WeaponCount", "DefenseCount", "CombatEfficiency", "DamageDealt", "DamageReceived"
        }

        for _, output in ipairs(combatOutputs) do
            if not self.Outputs[output] then
                self.Outputs[output] = {Value = 0}
            end
        end
    end

    -- Start combat system update loop
    local combatTimer = "ASC_CombatUpdate_" .. self:EntIndex()
    timer.Create(combatTimer, 0.5, 0, function() -- 2 FPS update rate
        if IsValid(self) and self.CombatSystem then
            self:UpdateCombatSystem()
        else
            timer.Remove(combatTimer)
        end
    end)

    -- Auto-detect weapons and defenses
    timer.Simple(1, function()
        if IsValid(self) then
            self:DetectCombatSystems()
        end
    end)

    print("[ASC Combat] Combat system initialized")
end

-- Update combat system
function ENT:UpdateCombatSystem()
    local combat = self.CombatSystem
    if not combat or not combat.enabled then return end

    local currentTime = CurTime()

    -- Update targeting system
    if combat.targetingSystem.enabled then
        self:UpdateTargetingSystem()
    end

    -- Update alert level based on threats
    self:UpdateAlertLevel()

    -- Update combat efficiency based on hull and shield status
    self:UpdateCombatEfficiency()

    -- Auto-targeting
    if combat.targetingSystem.autoTarget and not combat.targetingSystem.currentTarget then
        self:AutoSelectTarget()
    end

    -- Update networking
    self:UpdateCombatNetworking()

    -- Update wire outputs
    self:UpdateCombatWireOutputs()
end

-- Detect combat systems on ship
function ENT:DetectCombatSystems()
    local combat = self.CombatSystem
    if not combat or not self.ship or not self.ship.entities then return end

    combat.weapons = {}
    combat.defenses = {}

    for _, ent in ipairs(self.ship.entities) do
        if IsValid(ent) then
            local class = ent:GetClass()

            -- Detect weapons
            if string.find(class, "weapon") or string.find(class, "cannon") or
               string.find(class, "turret") or string.find(class, "railgun") then
                table.insert(combat.weapons, ent)
                ent.shipCore = self -- Link weapon to ship core
            end

            -- Detect defenses
            if string.find(class, "shield") or string.find(class, "defense") or
               string.find(class, "point_defense") or string.find(class, "countermeasure") then
                table.insert(combat.defenses, ent)
                ent.shipCore = self -- Link defense to ship core
            end
        end
    end

    print("[ASC Combat] Detected " .. #combat.weapons .. " weapons and " .. #combat.defenses .. " defenses")
end

-- Update targeting system
function ENT:UpdateTargetingSystem()
    local targeting = self.CombatSystem.targetingSystem
    if not targeting.enabled then return end

    -- Clear invalid targets
    for i = #targeting.targets, 1, -1 do
        local target = targeting.targets[i]
        if not IsValid(target) or self:GetPos():Distance(target:GetPos()) > targeting.range then
            table.remove(targeting.targets, i)
        end
    end

    -- Clear invalid current target
    if targeting.currentTarget and (not IsValid(targeting.currentTarget) or
       self:GetPos():Distance(targeting.currentTarget:GetPos()) > targeting.range) then
        targeting.currentTarget = nil
    end

    -- Scan for new targets
    local nearbyEnts = ents.FindInSphere(self:GetPos(), targeting.range)
    for _, ent in ipairs(nearbyEnts) do
        if self:IsValidTarget(ent) and not table.HasValue(targeting.targets, ent) then
            table.insert(targeting.targets, ent)
        end
    end
end

-- Check if entity is a valid target
function ENT:IsValidTarget(ent)
    if not IsValid(ent) then return false end
    if ent == self then return false end

    local targeting = self.CombatSystem.targetingSystem

    -- Check friendly fire
    if not targeting.friendlyFire then
        local owner = self:GetOwner()
        if IsValid(owner) and ent:GetOwner() == owner then
            return false
        end
    end

    -- Check if it's a ship core or weapon
    local class = ent:GetClass()
    if string.find(class, "ship_core") or string.find(class, "weapon") or
       string.find(class, "turret") or ent:IsPlayer() or ent:IsNPC() then
        return true
    end

    return false
end

-- Auto-select target
function ENT:AutoSelectTarget()
    local targeting = self.CombatSystem.targetingSystem
    if #targeting.targets == 0 then return end

    local bestTarget = nil
    local bestScore = -1

    for _, target in ipairs(targeting.targets) do
        if IsValid(target) then
            local score = self:CalculateTargetPriority(target)
            if score > bestScore then
                bestScore = score
                bestTarget = target
            end
        end
    end

    targeting.currentTarget = bestTarget
end

-- Calculate target priority
function ENT:CalculateTargetPriority(target)
    if not IsValid(target) then return 0 end

    local distance = self:GetPos():Distance(target:GetPos())
    local priority = self.CombatSystem.tacticalAI.priority

    local score = 0

    if priority == "closest" then
        score = 1000 - (distance / 10) -- Closer = higher score
    elseif priority == "strongest" then
        score = target:Health() or 100 -- Higher health = higher score
    elseif priority == "weakest" then
        score = 1000 - (target:Health() or 100) -- Lower health = higher score
    end

    -- Bonus for ship cores and weapons
    local class = target:GetClass()
    if string.find(class, "ship_core") then
        score = score + 500
    elseif string.find(class, "weapon") or string.find(class, "turret") then
        score = score + 200
    end

    return score
end

-- Update alert level
function ENT:UpdateAlertLevel()
    local combat = self.CombatSystem
    local targeting = combat.targetingSystem

    local threatCount = #targeting.targets
    local currentTime = CurTime()
    local timeSinceCombat = currentTime - combat.lastCombatTime

    local newAlertLevel = "GREEN"

    if threatCount > 0 and timeSinceCombat < 30 then
        newAlertLevel = "RED"
    elseif threatCount > 0 or timeSinceCombat < 60 then
        newAlertLevel = "YELLOW"
    end

    if combat.alertLevel ~= newAlertLevel then
        combat.alertLevel = newAlertLevel
        self:OnAlertLevelChanged(newAlertLevel)
    end
end

-- Update combat efficiency
function ENT:UpdateCombatEfficiency()
    local combat = self.CombatSystem
    local efficiency = 1.0

    -- Reduce efficiency based on hull damage
    if self.HullSystem then
        local hullIntegrity = self.HullSystem.currentHull / self.HullSystem.maxHull
        efficiency = efficiency * (0.5 + (hullIntegrity * 0.5)) -- 50-100% based on hull
    end

    -- Reduce efficiency based on shield status
    if self.ShieldSystem and not self.ShieldSystem.active then
        efficiency = efficiency * 0.8 -- 20% penalty for no shields
    end

    combat.combatEfficiency = efficiency
end

-- Alert level changed event
function ENT:OnAlertLevelChanged(newLevel)
    print("[ASC Combat] Alert level changed to: " .. newLevel)

    if newLevel == "RED" then
        -- Activate all defenses
        self:ActivateAllDefenses()
        -- Notify players
        self:NotifyPlayersOnShip("RED ALERT: Combat situation detected!")
    elseif newLevel == "YELLOW" then
        -- Prepare defenses
        self:PrepareDefenses()
        -- Notify players
        self:NotifyPlayersOnShip("YELLOW ALERT: Potential threats detected")
    elseif newLevel == "GREEN" then
        -- Stand down from alert
        self:NotifyPlayersOnShip("All clear - returning to normal operations")
    end
end

-- Activate all defenses
function ENT:ActivateAllDefenses()
    local combat = self.CombatSystem
    if not combat then return end

    for _, defense in ipairs(combat.defenses) do
        if IsValid(defense) and defense.Activate then
            defense:Activate()
        end
    end

    -- Activate shields if available
    if self.ShieldSystem and not self.ShieldSystem.active then
        self:ActivateShields()
    end
end

-- Prepare defenses
function ENT:PrepareDefenses()
    local combat = self.CombatSystem
    if not combat then return end

    for _, defense in ipairs(combat.defenses) do
        if IsValid(defense) and defense.Prepare then
            defense:Prepare()
        end
    end
end

-- Update combat networking
function ENT:UpdateCombatNetworking()
    local combat = self.CombatSystem
    if not combat then return end

    local targeting = combat.targetingSystem

    self:SetNWString("CombatMode", combat.combatMode)
    self:SetNWString("AlertLevel", combat.alertLevel)
    self:SetNWInt("TargetCount", #targeting.targets)
    self:SetNWEntity("CurrentTarget", targeting.currentTarget or NULL)
    self:SetNWInt("WeaponCount", #combat.weapons)
    self:SetNWInt("DefenseCount", #combat.defenses)
    self:SetNWFloat("CombatEfficiency", combat.combatEfficiency)
    self:SetNWFloat("DamageDealt", combat.totalDamageDealt)
    self:SetNWFloat("DamageReceived", combat.totalDamageReceived)
end

-- Update combat wire outputs
function ENT:UpdateCombatWireOutputs()
    local combat = self.CombatSystem
    if not combat or not self.Outputs then return end

    local targeting = combat.targetingSystem

    Wire_TriggerOutput(self, "CombatActive", combat.enabled and 1 or 0)
    Wire_TriggerOutput(self, "AlertLevel", combat.alertLevel)
    Wire_TriggerOutput(self, "TargetCount", #targeting.targets)
    Wire_TriggerOutput(self, "CurrentTarget", targeting.currentTarget or NULL)
    Wire_TriggerOutput(self, "WeaponCount", #combat.weapons)
    Wire_TriggerOutput(self, "DefenseCount", #combat.defenses)
    Wire_TriggerOutput(self, "CombatEfficiency", combat.combatEfficiency)
    Wire_TriggerOutput(self, "DamageDealt", combat.totalDamageDealt)
    Wire_TriggerOutput(self, "DamageReceived", combat.totalDamageReceived)
end

-- Initialize seat control system
function ENT:InitializeSeatControl()
    if not self.FlightSystem then return end

    -- Set up seat detection
    self:DetectShipSeats()

    -- Set up player monitoring hooks
    local hookName = "ASC_SeatControl_" .. self:EntIndex()

    -- Monitor player seat enter/exit
    hook.Add("PlayerEnteredVehicle", hookName, function(ply, vehicle)
        if not IsValid(self) then
            hook.Remove("PlayerEnteredVehicle", hookName)
            return
        end

        if self:IsSeatOnShip(vehicle) then
            self:OnPlayerEnteredSeat(ply, vehicle)
        end
    end)

    hook.Add("PlayerLeaveVehicle", hookName, function(ply, vehicle)
        if not IsValid(self) then
            hook.Remove("PlayerLeaveVehicle", hookName)
            return
        end

        if self:IsSeatOnShip(vehicle) then
            self:OnPlayerLeftSeat(ply, vehicle)
        end
    end)

    -- Set up networking for seat control
    self:SetNWBool("SeatControlEnabled", true)
    self:SetNWEntity("PilotSeat", NULL)
    self:SetNWEntity("Pilot", NULL)
    self:SetNWBool("SeatFlightActive", false)

    print("[ASC Flight] Seat control system initialized")
end

-- Detect seats on the ship
function ENT:DetectShipSeats()
    if not self.FlightSystem then return end

    local shipEntities = constraint.GetAllConstrainedEntities(self)
    local seats = {}

    if shipEntities then
        for ent, _ in pairs(shipEntities) do
            if IsValid(ent) and ent:IsVehicle() then
                table.insert(seats, ent)
            end
        end
    end

    self.FlightSystem.seatControl.availableSeats = seats
    self:SetNWInt("AvailableSeats", #seats)

    print("[ASC Flight] Detected " .. #seats .. " seats on ship")
    return seats
end

-- Check if seat is on this ship
function ENT:IsSeatOnShip(seat)
    if not IsValid(seat) or not self.FlightSystem then return false end

    local shipEntities = constraint.GetAllConstrainedEntities(self)
    if shipEntities and shipEntities[seat] then
        return true
    end

    return false
end

-- Player entered a seat on the ship
function ENT:OnPlayerEnteredSeat(player, seat)
    if not IsValid(player) or not IsValid(seat) or not self.FlightSystem then return end

    local sc = self.FlightSystem.seatControl

    -- Check if seat control is enabled
    if not sc.enabled then
        player:ChatPrint("[ASC Flight] Seat control is disabled")
        return
    end

    -- Check if another player is already piloting
    if IsValid(sc.pilot) and sc.pilot ~= player then
        player:ChatPrint("[ASC Flight] " .. sc.pilot:Name() .. " is already piloting this ship")
        return
    end

    -- Set this player as pilot
    sc.pilot = player
    sc.pilotSeat = seat

    -- Auto-activate flight system if enabled
    if sc.autoActivateOnSit then
        self.FlightSystem.active = true
        self.FlightSystem.mode = "MANUAL"
    end

    -- Send control interface to client
    self:SendSeatControlToClient(player, true)

    -- Update networking
    self:SetNWEntity("Pilot", player)
    self:SetNWEntity("PilotSeat", seat)
    self:SetNWBool("SeatFlightActive", true)

    player:ChatPrint("[ASC Flight] You are now piloting the ship")
    player:ChatPrint("[ASC Flight] Controls: WASD = Move, Space/Ctrl = Up/Down, Shift = Boost")
    player:ChatPrint("[ASC Flight] R = Autopilot, T = Auto-Level, X = Emergency Brake")

    print("[ASC Flight] " .. player:Name() .. " took control of ship from seat")
end

-- Player left a seat on the ship
function ENT:OnPlayerLeftSeat(player, seat)
    if not IsValid(player) or not self.FlightSystem then return end

    local sc = self.FlightSystem.seatControl

    -- Check if this player was the pilot
    if sc.pilot ~= player then return end

    -- Auto-deactivate flight system if enabled
    if sc.autoDeactivateOnExit then
        self.FlightSystem.active = false
        self:DeactivateFlightSystems()
    end

    -- Clear pilot
    sc.pilot = nil
    sc.pilotSeat = nil

    -- Send control release to client
    self:SendSeatControlToClient(player, false)

    -- Update networking
    self:SetNWEntity("Pilot", NULL)
    self:SetNWEntity("PilotSeat", NULL)
    self:SetNWBool("SeatFlightActive", false)

    player:ChatPrint("[ASC Flight] You are no longer piloting the ship")

    print("[ASC Flight] " .. player:Name() .. " released control of ship")
end

-- Send seat control interface to client
function ENT:SendSeatControlToClient(player, takeControl)
    if not IsValid(player) then return end

    net.Start("asc_seat_flight_control")
    net.WriteEntity(self)
    net.WriteBool(takeControl)
    if takeControl then
        net.WriteTable(self.FlightSystem.seatControl.keyBindings)
        net.WriteTable(self.FlightSystem.seatControl.sensitivity)
    end
    net.Send(player)
end

-- Process seat control inputs
function ENT:ProcessSeatControlInputs()
    local sc = self.FlightSystem.seatControl
    if not sc.enabled or not IsValid(sc.pilot) or not IsValid(sc.pilotSeat) then return end

    -- Check if pilot is still in seat
    if sc.pilot:GetVehicle() ~= sc.pilotSeat then
        self:OnPlayerLeftSeat(sc.pilot, sc.pilotSeat)
        return
    end

    -- Get input from client
    local inputData = sc.pilot:GetNWTable("ASC_FlightInput", {})
    if not inputData or table.IsEmpty(inputData) then return end

    local fs = self.FlightSystem
    local sensitivity = sc.sensitivity

    -- Process movement inputs
    fs.thrusters.forward = (inputData.forward or 0) * sensitivity.movement
    fs.thrusters.backward = (inputData.backward or 0) * sensitivity.movement
    fs.thrusters.left = (inputData.left or 0) * sensitivity.movement
    fs.thrusters.right = (inputData.right or 0) * sensitivity.movement
    fs.thrusters.up = (inputData.up or 0) * sensitivity.movement
    fs.thrusters.down = (inputData.down or 0) * sensitivity.movement

    -- Process mouse look for rotation
    if inputData.mouseX and inputData.mouseY then
        fs.thrusters.yawRight = math.Clamp(inputData.mouseX * sensitivity.rotation, -1, 1)
        fs.thrusters.pitchUp = math.Clamp(-inputData.mouseY * sensitivity.rotation, -1, 1)
    end

    -- Process boost
    local boostMultiplier = 1.0
    if inputData.boost then
        boostMultiplier = sensitivity.boost
    end

    -- Apply boost to all thrusters
    for key, value in pairs(fs.thrusters) do
        if key ~= "pitchUp" and key ~= "pitchDown" and key ~= "yawLeft" and key ~= "yawRight" and key ~= "rollLeft" and key ~= "rollRight" then
            fs.thrusters[key] = fs.thrusters[key] * boostMultiplier
        end
    end

    -- Process special commands
    if inputData.autopilot then
        fs.autopilot.active = not fs.autopilot.active
        if fs.autopilot.active then
            -- Set autopilot target ahead of ship
            local forward = sc.pilot:GetAimVector()
            fs.autopilot.target = self:GetPos() + forward * 2000
            sc.pilot:ChatPrint("[ASC Flight] Autopilot activated")
        else
            sc.pilot:ChatPrint("[ASC Flight] Autopilot deactivated")
        end
    end

    if inputData.autoLevel then
        fs.autoLevel = true
        sc.pilot:ChatPrint("[ASC Flight] Auto-level activated")
    end

    if inputData.brake then
        self:ActivateEmergencyBraking()
        sc.pilot:ChatPrint("[ASC Flight] Emergency brake activated")
    end

    if inputData.cloak then
        if self:ToggleCloaking() then
            local cloakState = fs.cloaking.active and "activated" or "deactivated"
            sc.pilot:ChatPrint("[ASC Flight] Cloaking " .. cloakState)
        end
    end

    -- Update last input time
    sc.lastInputTime = CurTime()
end

-- Update cloaking system
function ENT:UpdateCloakingSystem(deltaTime)
    local fs = self.FlightSystem
    if not fs or not fs.cloaking then return end

    local cloak = fs.cloaking
    local currentTime = CurTime()

    -- Update cooldown
    if cloak.cooldownTime > 0 then
        cloak.cooldownTime = math.max(0, cloak.cooldownTime - deltaTime)
    end

    -- Update disruption
    if cloak.disruptionTime > 0 then
        cloak.disruptionTime = math.max(0, cloak.disruptionTime - deltaTime)
        if cloak.disruptionTime <= 0 then
            print("[ASC Cloaking] Cloak disruption ended")
        end
    end

    -- Handle cloaking state changes
    if cloak.active and cloak.enabled and cloak.disruptionTime <= 0 then
        -- Cloaking up
        cloak.currentCharge = math.min(1.0, cloak.currentCharge + (deltaTime / cloak.chargeTime))

        -- Consume energy
        local energyCost = cloak.energyCost * deltaTime * cloak.efficiency
        if self.ResourceSystem and self.ResourceSystem.energy then
            if self.ResourceSystem.energy >= energyCost then
                self.ResourceSystem.energy = self.ResourceSystem.energy - energyCost
            else
                -- Not enough energy - disrupt cloaking
                self:DisruptCloaking("Insufficient energy")
            end
        end

    elseif not cloak.active or not cloak.enabled or cloak.disruptionTime > 0 then
        -- Decloaking
        cloak.currentCharge = math.max(0.0, cloak.currentCharge - (deltaTime / cloak.chargeTime))
    end

    -- Apply cloaking effects to ship entities
    self:ApplyCloakingEffects()

    -- Update networking
    self:UpdateCloakingNetworking()
end

-- Activate cloaking
function ENT:ActivateCloaking()
    local fs = self.FlightSystem
    if not fs or not fs.cloaking then return false end

    local cloak = fs.cloaking
    local currentTime = CurTime()

    -- Check if cloaking is enabled
    if not cloak.enabled then
        print("[ASC Cloaking] Cloaking system disabled")
        return false
    end

    -- Check cooldown
    if cloak.cooldownTime > 0 then
        print("[ASC Cloaking] Cloaking on cooldown: " .. math.Round(cloak.cooldownTime, 1) .. "s")
        return false
    end

    -- Check if disrupted
    if cloak.disruptionTime > 0 then
        print("[ASC Cloaking] Cloaking disrupted: " .. math.Round(cloak.disruptionTime, 1) .. "s")
        return false
    end

    -- Check energy requirements
    if self.ResourceSystem and self.ResourceSystem.energy then
        local energyRequired = cloak.energyCost * cloak.chargeTime
        if self.ResourceSystem.energy < energyRequired then
            print("[ASC Cloaking] Insufficient energy for cloaking")
            return false
        end
    end

    cloak.active = true
    cloak.lastToggleTime = currentTime

    print("[ASC Cloaking] Cloaking activated")
    self:PlayCloakingSound("activate")

    return true
end

-- Deactivate cloaking
function ENT:DeactivateCloaking()
    local fs = self.FlightSystem
    if not fs or not fs.cloaking then return false end

    local cloak = fs.cloaking
    local currentTime = CurTime()

    cloak.active = false
    cloak.lastToggleTime = currentTime
    cloak.cooldownTime = cloak.cooldownTime or 5.0

    print("[ASC Cloaking] Cloaking deactivated")
    self:PlayCloakingSound("deactivate")

    return true
end

-- Toggle cloaking
function ENT:ToggleCloaking()
    local fs = self.FlightSystem
    if not fs or not fs.cloaking then return false end

    if fs.cloaking.active then
        return self:DeactivateCloaking()
    else
        return self:ActivateCloaking()
    end
end

-- Disrupt cloaking (from damage, EMP, etc.)
function ENT:DisruptCloaking(reason)
    local fs = self.FlightSystem
    if not fs or not fs.cloaking then return end

    local cloak = fs.cloaking

    cloak.active = false
    cloak.disruptionTime = cloak.maxDisruptionTime
    cloak.cooldownTime = cloak.cooldownTime or 5.0

    print("[ASC Cloaking] Cloaking disrupted: " .. (reason or "Unknown"))
    self:PlayCloakingSound("disrupt")
end

-- Apply cloaking effects to ship entities
function ENT:ApplyCloakingEffects()
    local fs = self.FlightSystem
    if not fs or not fs.cloaking then return end

    local cloak = fs.cloaking
    local alpha = math.max(0, 255 * (1.0 - cloak.currentCharge * cloak.efficiency))

    -- Get all ship entities
    local shipEntities = constraint.GetAllConstrainedEntities(self)
    if not shipEntities then return end

    -- Apply transparency to all ship entities
    for ent, _ in pairs(shipEntities) do
        if IsValid(ent) then
            local color = ent:GetColor()
            color.a = alpha
            ent:SetColor(color)

            -- Set render mode for transparency
            if alpha < 255 then
                ent:SetRenderMode(RENDERMODE_TRANSALPHA)
            else
                ent:SetRenderMode(RENDERMODE_NORMAL)
            end
        end
    end
end

-- Update cloaking networking
function ENT:UpdateCloakingNetworking()
    local fs = self.FlightSystem
    if not fs or not fs.cloaking then return end

    local cloak = fs.cloaking

    -- Update networked variables
    self:SetNWBool("CloakingActive", cloak.active)
    self:SetNWFloat("CloakingCharge", cloak.currentCharge)
    self:SetNWBool("CloakingEnabled", cloak.enabled)
    self:SetNWFloat("CloakingEfficiency", cloak.efficiency)
    self:SetNWFloat("CloakingCooldown", cloak.cooldownTime)
    self:SetNWBool("CloakingDisrupted", cloak.disruptionTime > 0)

    -- Update wire outputs
    if self.Outputs then
        Wire_TriggerOutput(self, "CloakingActive", cloak.active and 1 or 0)
        Wire_TriggerOutput(self, "CloakingCharge", cloak.currentCharge)
        Wire_TriggerOutput(self, "CloakingEfficiency", cloak.efficiency)
        Wire_TriggerOutput(self, "CloakingCooldown", cloak.cooldownTime)
        Wire_TriggerOutput(self, "CloakingDisrupted", cloak.disruptionTime > 0 and 1 or 0)
        Wire_TriggerOutput(self, "CloakingEnergyUsage", cloak.active and cloak.energyCost or 0)
    end
end

-- Play cloaking sounds
function ENT:PlayCloakingSound(soundType)
    local sounds = {
        activate = "ambient/energy/weld1.wav",
        deactivate = "ambient/energy/weld2.wav",
        disrupt = "ambient/energy/spark1.wav"
    }

    local sound = sounds[soundType]
    if sound then
        self:EmitSound(sound, 75, 100, 0.8)
    end
end

-- Main flight system update function
function ENT:UpdateFlightSystem()
    if not self.FlightSystem or not self.FlightSystem.active then return end

    local deltaTime = 0.05 -- 20 FPS update rate

    -- Update wire inputs
    self:ProcessFlightInputs()

    -- Update seat control inputs
    self:ProcessSeatControlInputs()

    -- Update cloaking system
    self:UpdateCloakingSystem(deltaTime)

    -- Update physics
    self:UpdateFlightPhysics(deltaTime)

    -- Update autopilot
    if self.FlightSystem.autopilot.active then
        self:UpdateAutopilot(deltaTime)
    end

    -- Update formation flying
    if self.FlightSystem.formation.active then
        self:UpdateFormationFlying(deltaTime)
    end

    -- Update docking system
    if self.FlightSystem.docking.active then
        self:UpdateDockingSystem(deltaTime)
    end

    -- Update safety systems
    self:UpdateFlightSafety(deltaTime)

    -- Apply movement to ship
    self:ApplyFlightMovement(deltaTime)

    -- Update networking
    self:UpdateFlightNetworking()

    -- Update statistics
    self:UpdateFlightStatistics(deltaTime)
end

-- Process wire inputs for flight control
function ENT:ProcessFlightInputs()
    if not self.Inputs then return end

    local fs = self.FlightSystem

    -- Update thruster inputs
    fs.thrusters.forward = math.Clamp(self.Inputs.ThrustForward.Value or 0, -1, 1)
    fs.thrusters.backward = math.Clamp(self.Inputs.ThrustBackward.Value or 0, -1, 1)
    fs.thrusters.left = math.Clamp(self.Inputs.ThrustLeft.Value or 0, -1, 1)
    fs.thrusters.right = math.Clamp(self.Inputs.ThrustRight.Value or 0, -1, 1)
    fs.thrusters.up = math.Clamp(self.Inputs.ThrustUp.Value or 0, -1, 1)
    fs.thrusters.down = math.Clamp(self.Inputs.ThrustDown.Value or 0, -1, 1)

    -- Update rotational inputs
    fs.thrusters.pitchUp = math.Clamp(self.Inputs.PitchUp.Value or 0, -1, 1)
    fs.thrusters.pitchDown = math.Clamp(self.Inputs.PitchDown.Value or 0, -1, 1)
    fs.thrusters.yawLeft = math.Clamp(self.Inputs.YawLeft.Value or 0, -1, 1)
    fs.thrusters.yawRight = math.Clamp(self.Inputs.YawRight.Value or 0, -1, 1)
    fs.thrusters.rollLeft = math.Clamp(self.Inputs.RollLeft.Value or 0, -1, 1)
    fs.thrusters.rollRight = math.Clamp(self.Inputs.RollRight.Value or 0, -1, 1)

    -- Update flight mode
    local flightActive = (self.Inputs.FlightActive.Value or 0) > 0
    if flightActive ~= fs.active then
        fs.active = flightActive
        if flightActive then
            print("[ASC Flight] Flight system activated")
        else
            print("[ASC Flight] Flight system deactivated")
            self:DeactivateFlightSystems()
        end
    end

    -- Update flight mode
    local modeInput = self.Inputs.FlightMode.Value or 0
    local modes = {"MANUAL", "AUTOPILOT", "FORMATION", "DOCKING"}
    local newMode = modes[math.Clamp(math.floor(modeInput) + 1, 1, #modes)]
    if newMode ~= fs.mode then
        fs.mode = newMode
        print("[ASC Flight] Flight mode changed to: " .. newMode)
    end

    -- Update system settings
    fs.inertialDampening = (self.Inputs.InertialDampening.Value or 1) > 0
    fs.stabilityAssist = (self.Inputs.StabilityAssist.Value or 1) > 0
    fs.collisionAvoidance = (self.Inputs.CollisionAvoidance.Value or 1) > 0

    -- Update autopilot
    local autopilotActive = (self.Inputs.AutopilotActive.Value or 0) > 0
    if autopilotActive ~= fs.autopilot.active then
        fs.autopilot.active = autopilotActive
        if autopilotActive then
            local targetX = self.Inputs.AutopilotTargetX.Value or 0
            local targetY = self.Inputs.AutopilotTargetY.Value or 0
            local targetZ = self.Inputs.AutopilotTargetZ.Value or 0
            fs.autopilot.target = Vector(targetX, targetY, targetZ)
            print("[ASC Flight] Autopilot activated, target: " .. tostring(fs.autopilot.target))
        else
            print("[ASC Flight] Autopilot deactivated")
        end
    end

    -- Handle waypoint commands
    if (self.Inputs.AddWaypoint.Value or 0) > 0 then
        local targetX = self.Inputs.AutopilotTargetX.Value or 0
        local targetY = self.Inputs.AutopilotTargetY.Value or 0
        local targetZ = self.Inputs.AutopilotTargetZ.Value or 0
        self:AddWaypoint(Vector(targetX, targetY, targetZ))
    end

    if (self.Inputs.ClearWaypoints.Value or 0) > 0 then
        self:ClearWaypoints()
    end

    -- Handle emergency brake
    if (self.Inputs.EmergencyBrake.Value or 0) > 0 then
        self:ActivateEmergencyBraking()
    end

    -- Handle auto-level
    if (self.Inputs.AutoLevel.Value or 0) > 0 then
        fs.autoLevel = true
    end

    -- Handle cloaking inputs
    if (self.Inputs.CloakingActivate and self.Inputs.CloakingActivate.Value or 0) > 0 then
        self:ActivateCloaking()
    end

    if (self.Inputs.CloakingDeactivate and self.Inputs.CloakingDeactivate.Value or 0) > 0 then
        self:DeactivateCloaking()
    end

    if (self.Inputs.CloakingToggle and self.Inputs.CloakingToggle.Value or 0) > 0 then
        self:ToggleCloaking()
    end

    if self.Inputs.CloakingEfficiency then
        fs.cloaking.efficiency = math.Clamp(self.Inputs.CloakingEfficiency.Value or 1.0, 0.1, 1.0)
    end

    if self.Inputs.CloakingEnabled then
        fs.cloaking.enabled = (self.Inputs.CloakingEnabled.Value or 1) > 0
    end
end

-- Update flight physics (6DOF movement)
function ENT:UpdateFlightPhysics(deltaTime)
    local fs = self.FlightSystem
    if not fs then return end

    -- Calculate thrust forces
    local thrustVector = Vector(0, 0, 0)
    local angularThrust = Angle(0, 0, 0)

    -- Linear thrust (6DOF)
    thrustVector.x = (fs.thrusters.up - fs.thrusters.down) * fs.maxThrust
    thrustVector.y = (fs.thrusters.right - fs.thrusters.left) * fs.maxThrust
    thrustVector.z = (fs.thrusters.forward - fs.thrusters.backward) * fs.maxThrust

    -- Angular thrust (6DOF rotation)
    angularThrust.p = (fs.thrusters.pitchUp - fs.thrusters.pitchDown) * fs.maxAngularThrust
    angularThrust.y = (fs.thrusters.yawRight - fs.thrusters.yawLeft) * fs.maxAngularThrust
    angularThrust.r = (fs.thrusters.rollRight - fs.thrusters.rollLeft) * fs.maxAngularThrust

    -- Apply thrust to acceleration
    fs.acceleration = thrustVector / fs.mass
    fs.angularAcceleration = angularThrust

    -- Update velocity
    fs.velocity = fs.velocity + (fs.acceleration * deltaTime)
    fs.angularVelocity = fs.angularVelocity + (fs.angularAcceleration * deltaTime)

    -- Apply inertial dampening
    if fs.inertialDampening then
        fs.velocity = fs.velocity * fs.dampeningStrength
        fs.angularVelocity = fs.angularVelocity * fs.angularDampening
    end

    -- Apply cloaking movement penalty
    if fs.cloaking and fs.cloaking.active and fs.cloaking.currentCharge > 0 then
        local penalty = fs.cloaking.movementPenalty * fs.cloaking.currentCharge
        fs.velocity = fs.velocity * (1.0 - penalty)
        fs.angularVelocity = fs.angularVelocity * (1.0 - penalty * 0.5)
    end

    -- Apply speed limits
    local speed = fs.velocity:Length()
    if speed > fs.maxSpeed then
        fs.velocity = fs.velocity:GetNormalized() * fs.maxSpeed
    end

    -- Apply angular speed limits
    if fs.angularVelocity:Length() > fs.maxAngularSpeed then
        fs.angularVelocity = fs.angularVelocity:GetNormalized() * fs.maxAngularSpeed
    end

    -- Auto-level system
    if fs.autoLevel then
        self:ApplyAutoLevel(deltaTime)
    end

    -- Stability assist
    if fs.stabilityAssist then
        self:ApplyStabilityAssist(deltaTime)
    end
end

-- Apply movement to ship entities
function ENT:ApplyFlightMovement(deltaTime)
    local fs = self.FlightSystem
    if not fs or not fs.active then return end

    -- Get all ship entities
    local shipEntities = constraint.GetAllConstrainedEntities(self)
    if not shipEntities then return end

    local movement = fs.velocity * deltaTime
    local rotation = fs.angularVelocity * deltaTime

    -- Apply movement to all ship entities
    for ent, _ in pairs(shipEntities) do
        if IsValid(ent) then
            local phys = ent:GetPhysicsObject()
            if IsValid(phys) then
                -- Apply linear movement
                local currentPos = ent:GetPos()
                local newPos = currentPos + movement
                phys:SetPos(newPos)

                -- Apply rotational movement
                if rotation:Length() > 0.1 then
                    local currentAngles = ent:GetAngles()
                    local newAngles = currentAngles + rotation
                    ent:SetAngles(newAngles)
                end

                -- Set physics velocity for smooth movement
                phys:SetVelocity(fs.velocity)
                phys:SetAngleVelocity(Vector(rotation.p, rotation.y, rotation.r))
            end
        end
    end
end

-- Auto-level system
function ENT:ApplyAutoLevel(deltaTime)
    local fs = self.FlightSystem
    if not fs then return end

    local currentAngles = self:GetAngles()
    local targetAngles = Angle(0, currentAngles.y, 0) -- Keep yaw, level pitch and roll

    local angleDiff = targetAngles - currentAngles
    angleDiff:Normalize()

    -- Apply corrective angular thrust
    local correctionStrength = 2.0
    fs.angularAcceleration = fs.angularAcceleration + (angleDiff * correctionStrength)

    -- Disable auto-level once close to level
    if math.abs(angleDiff.p) < 2 and math.abs(angleDiff.r) < 2 then
        fs.autoLevel = false
    end
end

-- Stability assist system
function ENT:ApplyStabilityAssist(deltaTime)
    local fs = self.FlightSystem
    if not fs then return end

    -- Reduce unwanted rotation when not actively rotating
    local rotationInput = math.abs(fs.thrusters.pitchUp - fs.thrusters.pitchDown) +
                         math.abs(fs.thrusters.yawLeft - fs.thrusters.yawRight) +
                         math.abs(fs.thrusters.rollLeft - fs.thrusters.rollRight)

    if rotationInput < 0.1 then
        -- Apply stability dampening
        fs.angularVelocity = fs.angularVelocity * 0.95
    end
end

-- Update autopilot system
function ENT:UpdateAutopilot(deltaTime)
    local fs = self.FlightSystem
    if not fs or not fs.autopilot.active then return end

    local target = fs.autopilot.target
    if not target then return end

    local currentPos = self:GetPos()
    local direction = (target - currentPos):GetNormalized()
    local distance = currentPos:Distance(target)

    -- Check if reached target
    if distance <= fs.autopilot.accuracy then
        self:ReachedAutopilotTarget()
        return
    end

    -- Calculate autopilot thrust
    local thrustPower = math.min(1.0, distance / 1000) -- Scale thrust based on distance
    local autopilotSpeed = fs.autopilot.speed / fs.maxSpeed

    -- Apply autopilot thrust
    fs.thrusters.forward = direction.z * thrustPower * autopilotSpeed
    fs.thrusters.right = direction.y * thrustPower * autopilotSpeed
    fs.thrusters.up = direction.x * thrustPower * autopilotSpeed

    -- Face the target
    local targetAngle = direction:Angle()
    local currentAngle = self:GetAngles()
    local angleDiff = targetAngle - currentAngle
    angleDiff:Normalize()

    -- Apply rotational correction
    local rotationStrength = 0.5
    fs.thrusters.pitchUp = math.Clamp(angleDiff.p * rotationStrength, -1, 1)
    fs.thrusters.yawRight = math.Clamp(angleDiff.y * rotationStrength, -1, 1)

    -- Collision avoidance
    if fs.collisionAvoidance then
        self:ApplyCollisionAvoidance(direction)
    end
end

-- Reached autopilot target
function ENT:ReachedAutopilotTarget()
    local fs = self.FlightSystem
    if not fs then return end

    print("[ASC Flight] Reached autopilot target")

    -- Check for waypoints
    if #fs.autopilot.waypoints > 0 then
        fs.autopilot.currentWaypoint = fs.autopilot.currentWaypoint + 1

        if fs.autopilot.currentWaypoint <= #fs.autopilot.waypoints then
            fs.autopilot.target = fs.autopilot.waypoints[fs.autopilot.currentWaypoint]
            print("[ASC Flight] Moving to waypoint " .. fs.autopilot.currentWaypoint)
        else
            -- All waypoints completed
            fs.autopilot.active = false
            fs.autopilot.currentWaypoint = 1
            fs.stats.waypointsCompleted = fs.stats.waypointsCompleted + #fs.autopilot.waypoints
            print("[ASC Flight] All waypoints completed")
        end
    else
        -- Single target reached
        fs.autopilot.active = false
        print("[ASC Flight] Autopilot target reached")
    end
end

-- Add waypoint to autopilot
function ENT:AddWaypoint(position)
    local fs = self.FlightSystem
    if not fs then return end

    table.insert(fs.autopilot.waypoints, position)
    print("[ASC Flight] Waypoint added: " .. tostring(position) .. " (Total: " .. #fs.autopilot.waypoints .. ")")
end

-- Clear all waypoints
function ENT:ClearWaypoints()
    local fs = self.FlightSystem
    if not fs then return end

    fs.autopilot.waypoints = {}
    fs.autopilot.currentWaypoint = 1
    print("[ASC Flight] All waypoints cleared")
end

-- Update flight safety systems
function ENT:UpdateFlightSafety(deltaTime)
    local fs = self.FlightSystem
    if not fs then return end

    local currentTime = CurTime()

    -- Collision detection (every 0.2 seconds)
    if currentTime - fs.safety.lastCollisionCheck > 0.2 then
        fs.safety.lastCollisionCheck = currentTime
        self:CheckForCollisions()
    end

    -- Proximity warning
    self:CheckProximityWarning()

    -- Emergency braking
    if fs.safety.emergencyBraking then
        self:ApplyEmergencyBraking(deltaTime)
    end
end

-- Check for potential collisions
function ENT:CheckForCollisions()
    local fs = self.FlightSystem
    if not fs then return end

    local currentPos = self:GetPos()
    local velocity = fs.velocity

    if velocity:Length() < 50 then return end -- Skip if moving slowly

    -- Trace ahead in movement direction
    local traceDistance = velocity:Length() * 2 -- 2 seconds ahead
    local traceEnd = currentPos + velocity:GetNormalized() * traceDistance

    local trace = util.TraceLine({
        start = currentPos,
        endpos = traceEnd,
        filter = function(ent)
            -- Filter out ship entities
            local shipEntities = constraint.GetAllConstrainedEntities(self)
            if shipEntities and shipEntities[ent] then
                return false
            end
            return true
        end
    })

    if trace.Hit and trace.Fraction < 0.8 then
        fs.safety.collisionWarning = true

        if trace.Fraction < 0.3 then
            -- Imminent collision - activate emergency braking
            self:ActivateEmergencyBraking()
        end
    else
        fs.safety.collisionWarning = false
    end
end

-- Check proximity warning
function ENT:CheckProximityWarning()
    local fs = self.FlightSystem
    if not fs then return end

    local nearbyEntities = ents.FindInSphere(self:GetPos(), 500)
    local proximityCount = 0

    for _, ent in ipairs(nearbyEntities) do
        if IsValid(ent) and ent ~= self and not ent:IsPlayer() then
            -- Check if it's not part of our ship
            local shipEntities = constraint.GetAllConstrainedEntities(self)
            if not shipEntities or not shipEntities[ent] then
                proximityCount = proximityCount + 1
            end
        end
    end

    fs.safety.proximityAlert = proximityCount > 5
end

-- Activate emergency braking
function ENT:ActivateEmergencyBraking()
    local fs = self.FlightSystem
    if not fs then return end

    fs.safety.emergencyBraking = true
    print("[ASC Flight] Emergency braking activated!")

    -- Auto-disable after 3 seconds
    timer.Simple(3, function()
        if IsValid(self) and fs then
            fs.safety.emergencyBraking = false
            print("[ASC Flight] Emergency braking deactivated")
        end
    end)
end

-- Apply emergency braking
function ENT:ApplyEmergencyBraking(deltaTime)
    local fs = self.FlightSystem
    if not fs then return end

    -- Apply strong reverse thrust
    local brakeStrength = 2.0
    fs.velocity = fs.velocity * (1 - brakeStrength * deltaTime)
    fs.angularVelocity = fs.angularVelocity * 0.9
end

-- Update flight networking
function ENT:UpdateFlightNetworking()
    local fs = self.FlightSystem
    if not fs then return end

    -- Update flight status
    self:SetNWBool("FlightActive", fs.active)
    self:SetNWString("FlightMode", fs.mode)
    self:SetNWFloat("FlightSpeed", fs.velocity:Length())

    -- Update thruster states
    self:SetNWFloat("ThrusterForward", fs.thrusters.forward)
    self:SetNWFloat("ThrusterBackward", fs.thrusters.backward)
    self:SetNWFloat("ThrusterLeft", fs.thrusters.left)
    self:SetNWFloat("ThrusterRight", fs.thrusters.right)
    self:SetNWFloat("ThrusterUp", fs.thrusters.up)
    self:SetNWFloat("ThrusterDown", fs.thrusters.down)

    -- Update autopilot status
    self:SetNWBool("AutopilotActive", fs.autopilot.active)
    if fs.autopilot.target then
        self:SetNWVector("AutopilotTarget", fs.autopilot.target)
    end
    self:SetNWInt("WaypointCount", #fs.autopilot.waypoints)
    self:SetNWInt("CurrentWaypoint", fs.autopilot.currentWaypoint)

    -- Update safety systems
    self:SetNWBool("InertialDampening", fs.inertialDampening)
    self:SetNWBool("StabilityAssist", fs.stabilityAssist)
    self:SetNWBool("CollisionAvoidance", fs.collisionAvoidance)
    self:SetNWBool("ProximityWarning", fs.safety.proximityAlert)
    self:SetNWBool("EmergencyBraking", fs.safety.emergencyBraking)
    self:SetNWBool("CollisionWarning", fs.safety.collisionWarning)

    -- Update wire outputs
    if self.Outputs then
        Wire_TriggerOutput(self, "FlightActive", fs.active and 1 or 0)
        Wire_TriggerOutput(self, "FlightSpeed", fs.velocity:Length())
        Wire_TriggerOutput(self, "FlightMass", fs.mass)
        Wire_TriggerOutput(self, "FlightMode", fs.mode == "MANUAL" and 0 or (fs.mode == "AUTOPILOT" and 1 or 2))

        Wire_TriggerOutput(self, "VelocityX", fs.velocity.x)
        Wire_TriggerOutput(self, "VelocityY", fs.velocity.y)
        Wire_TriggerOutput(self, "VelocityZ", fs.velocity.z)
        Wire_TriggerOutput(self, "Speed", fs.velocity:Length())

        local pos = self:GetPos()
        Wire_TriggerOutput(self, "PositionX", pos.x)
        Wire_TriggerOutput(self, "PositionY", pos.y)
        Wire_TriggerOutput(self, "PositionZ", pos.z)

        Wire_TriggerOutput(self, "AutopilotActive", fs.autopilot.active and 1 or 0)
        if fs.autopilot.target then
            Wire_TriggerOutput(self, "DistanceToTarget", self:GetPos():Distance(fs.autopilot.target))
        end
        Wire_TriggerOutput(self, "WaypointsRemaining", #fs.autopilot.waypoints - fs.autopilot.currentWaypoint + 1)

        Wire_TriggerOutput(self, "CollisionWarning", fs.safety.collisionWarning and 1 or 0)
        Wire_TriggerOutput(self, "ProximityAlert", fs.safety.proximityAlert and 1 or 0)
        Wire_TriggerOutput(self, "EmergencyBraking", fs.safety.emergencyBraking and 1 or 0)
    end
end

-- Update flight statistics
function ENT:UpdateFlightStatistics(deltaTime)
    local fs = self.FlightSystem
    if not fs then return end

    if fs.active then
        -- Update flight time
        fs.stats.flightTime = fs.stats.flightTime + deltaTime

        -- Update distance traveled
        local distance = fs.velocity:Length() * deltaTime
        fs.stats.distanceTraveled = fs.stats.distanceTraveled + distance

        -- Update max speed reached
        local currentSpeed = fs.velocity:Length()
        if currentSpeed > fs.stats.maxSpeedReached then
            fs.stats.maxSpeedReached = currentSpeed
        end

        -- Update energy consumption (based on thrust usage)
        local thrustUsage = math.abs(fs.thrusters.forward) + math.abs(fs.thrusters.backward) +
                           math.abs(fs.thrusters.left) + math.abs(fs.thrusters.right) +
                           math.abs(fs.thrusters.up) + math.abs(fs.thrusters.down)
        fs.stats.energyConsumed = fs.stats.energyConsumed + (thrustUsage * deltaTime * 10)
    end
end

-- Deactivate flight systems
function ENT:DeactivateFlightSystems()
    local fs = self.FlightSystem
    if not fs then return end

    -- Reset all thrusters
    for key, _ in pairs(fs.thrusters) do
        fs.thrusters[key] = 0
    end

    -- Deactivate autopilot
    fs.autopilot.active = false

    -- Deactivate formation flying
    fs.formation.active = false

    -- Deactivate docking
    fs.docking.active = false

    -- Reset safety systems
    fs.safety.emergencyBraking = false
    fs.safety.collisionWarning = false
    fs.safety.proximityAlert = false

    print("[ASC Flight] All flight systems deactivated")
end

-- Collision avoidance system
function ENT:ApplyCollisionAvoidance(direction)
    local fs = self.FlightSystem
    if not fs then return end

    local avoidanceRadius = fs.autopilot.avoidanceRadius
    local nearbyEntities = ents.FindInSphere(self:GetPos(), avoidanceRadius)

    for _, ent in ipairs(nearbyEntities) do
        if IsValid(ent) and ent ~= self then
            -- Check if it's not part of our ship
            local shipEntities = constraint.GetAllConstrainedEntities(self)
            if not shipEntities or not shipEntities[ent] then
                local entPos = ent:GetPos()
                local myPos = self:GetPos()
                local avoidDirection = (myPos - entPos):GetNormalized()

                -- Apply avoidance thrust
                local avoidanceStrength = 0.3
                fs.thrusters.forward = fs.thrusters.forward + (avoidDirection.z * avoidanceStrength)
                fs.thrusters.right = fs.thrusters.right + (avoidDirection.y * avoidanceStrength)
                fs.thrusters.up = fs.thrusters.up + (avoidDirection.x * avoidanceStrength)

                -- Clamp values
                fs.thrusters.forward = math.Clamp(fs.thrusters.forward, -1, 1)
                fs.thrusters.right = math.Clamp(fs.thrusters.right, -1, 1)
                fs.thrusters.up = math.Clamp(fs.thrusters.up, -1, 1)
            end
        end
    end
end

-- Console commands for flight system
concommand.Add("asc_flight_activate", function(ply, cmd, args)
    if not IsValid(ply) then return end

    local trace = ply:GetEyeTrace()
    if not IsValid(trace.Entity) or trace.Entity:GetClass() ~= "asc_ship_core" then
        ply:ChatPrint("[ASC Flight] Look at an ASC ship core")
        return
    end

    local core = trace.Entity
    if core.FlightSystem then
        core.FlightSystem.active = true
        ply:ChatPrint("[ASC Flight] Flight system activated")
    else
        ply:ChatPrint("[ASC Flight] Enhanced flight system not available")
    end
end)

-- Cloaking console commands
concommand.Add("asc_cloak_activate", function(ply, cmd, args)
    if not IsValid(ply) then return end

    local trace = ply:GetEyeTrace()
    if not IsValid(trace.Entity) or trace.Entity:GetClass() ~= "asc_ship_core" then
        ply:ChatPrint("[ASC Cloaking] Look at an ASC ship core")
        return
    end

    local core = trace.Entity
    if core.ActivateCloaking then
        if core:ActivateCloaking() then
            ply:ChatPrint("[ASC Cloaking] Cloaking activated")
        else
            ply:ChatPrint("[ASC Cloaking] Failed to activate cloaking")
        end
    else
        ply:ChatPrint("[ASC Cloaking] Cloaking system not available")
    end
end)

concommand.Add("asc_cloak_deactivate", function(ply, cmd, args)
    if not IsValid(ply) then return end

    local trace = ply:GetEyeTrace()
    if not IsValid(trace.Entity) or trace.Entity:GetClass() ~= "asc_ship_core" then
        ply:ChatPrint("[ASC Cloaking] Look at an ASC ship core")
        return
    end

    local core = trace.Entity
    if core.DeactivateCloaking then
        if core:DeactivateCloaking() then
            ply:ChatPrint("[ASC Cloaking] Cloaking deactivated")
        else
            ply:ChatPrint("[ASC Cloaking] Failed to deactivate cloaking")
        end
    else
        ply:ChatPrint("[ASC Cloaking] Cloaking system not available")
    end
end)

concommand.Add("asc_cloak_toggle", function(ply, cmd, args)
    if not IsValid(ply) then return end

    local trace = ply:GetEyeTrace()
    if not IsValid(trace.Entity) or trace.Entity:GetClass() ~= "asc_ship_core" then
        ply:ChatPrint("[ASC Cloaking] Look at an ASC ship core")
        return
    end

    local core = trace.Entity
    if core.ToggleCloaking then
        if core:ToggleCloaking() then
            local state = core.FlightSystem.cloaking.active and "activated" or "deactivated"
            ply:ChatPrint("[ASC Cloaking] Cloaking " .. state)
        else
            ply:ChatPrint("[ASC Cloaking] Failed to toggle cloaking")
        end
    else
        ply:ChatPrint("[ASC Cloaking] Cloaking system not available")
    end
end)

concommand.Add("asc_cloak_status", function(ply, cmd, args)
    if not IsValid(ply) then return end

    local trace = ply:GetEyeTrace()
    if not IsValid(trace.Entity) or trace.Entity:GetClass() ~= "asc_ship_core" then
        ply:ChatPrint("[ASC Cloaking] Look at an ASC ship core")
        return
    end

    local core = trace.Entity
    if core.FlightSystem and core.FlightSystem.cloaking then
        local cloak = core.FlightSystem.cloaking
        ply:ChatPrint("[ASC Cloaking] === Cloaking System Status ===")
        ply:ChatPrint("Active: " .. (cloak.active and "Yes" or "No"))
        ply:ChatPrint("Enabled: " .. (cloak.enabled and "Yes" or "No"))
        ply:ChatPrint("Charge: " .. math.Round(cloak.currentCharge * 100) .. "%")
        ply:ChatPrint("Efficiency: " .. math.Round(cloak.efficiency * 100) .. "%")
        ply:ChatPrint("Energy Cost: " .. cloak.energyCost .. "/sec")
        ply:ChatPrint("Cooldown: " .. math.Round(cloak.cooldownTime, 1) .. "s")
        ply:ChatPrint("Disrupted: " .. (cloak.disruptionTime > 0 and "Yes (" .. math.Round(cloak.disruptionTime, 1) .. "s)" or "No"))
    else
        ply:ChatPrint("[ASC Cloaking] Cloaking system not available")
    end
end)

-- Enhanced damage handling with combat integration
function ENT:OnTakeDamage(dmginfo)
    if not IsValid(dmginfo) then return end

    local damage = dmginfo:GetDamage()
    if damage <= 0 then return end

    local attacker = dmginfo:GetAttacker()
    local damageType = dmginfo:GetDamageType()
    local hitPos = dmginfo:GetDamagePosition()

    -- Apply damage resistance
    if self.HullSystem and self.HullSystem.damageResistance then
        local resistance = 0

        if bit.band(damageType, DMG_BULLET) > 0 then
            resistance = self.HullSystem.damageResistance.kinetic
        elseif bit.band(damageType, DMG_ENERGYBEAM) > 0 then
            resistance = self.HullSystem.damageResistance.energy
        elseif bit.band(damageType, DMG_BLAST) > 0 then
            resistance = self.HullSystem.damageResistance.explosive
        elseif bit.band(damageType, DMG_BURN) > 0 then
            resistance = self.HullSystem.damageResistance.thermal
        end

        damage = damage * (1 - resistance * self.HullSystem.armorRating)
    end

    -- Disrupt cloaking when taking damage
    if self.FlightSystem and self.FlightSystem.cloaking and self.FlightSystem.cloaking.active then
        if damage > 10 then -- Only significant damage disrupts cloaking
            self:DisruptCloaking("Hull damage")
        end
    end

    -- Apply damage to built-in hull system
    if self.HullSystem then
        local oldHull = self.HullSystem.currentHull
        self.HullSystem.currentHull = math.max(0, self.HullSystem.currentHull - damage)
        self.HullSystem.totalDamageReceived = self.HullSystem.totalDamageReceived + damage
        self.HullSystem.lastDamageTime = CurTime()

        -- Add to damage history
        table.insert(self.HullSystem.damageHistory, {
            damage = damage,
            attacker = attacker,
            damageType = damageType,
            position = hitPos,
            time = CurTime()
        })

        -- Limit damage history
        if #self.HullSystem.damageHistory > 50 then
            table.remove(self.HullSystem.damageHistory, 1)
        end

        -- Update networking immediately
        local integrity = self.HullSystem.currentHull / self.HullSystem.maxHull
        self:SetNWFloat("HullIntegrity", integrity)

        if self.HullSystem.currentHull <= 0 then
            self:OnHullDestroyed()
        end
    end

    -- Update combat system
    if self.CombatSystem then
        self.CombatSystem.totalDamageReceived = self.CombatSystem.totalDamageReceived + damage
        self.CombatSystem.lastCombatTime = CurTime()

        -- Add attacker to target list if valid
        if IsValid(attacker) and self:IsValidTarget(attacker) then
            local targeting = self.CombatSystem.targetingSystem
            if not table.HasValue(targeting.targets, attacker) then
                table.insert(targeting.targets, attacker)
            end

            -- Auto-target attacker if no current target
            if not targeting.currentTarget then
                targeting.currentTarget = attacker
            end
        end
    end

    -- Damage effects
    self:CreateDamageEffects(hitPos, damage)
end

-- Create enhanced damage effects
function ENT:CreateDamageEffects(pos, damage)
    if not pos then pos = self:GetPos() end
    damage = damage or 10

    -- Scale effects based on damage
    local scale = math.Clamp(damage / 50, 0.5, 3.0)
    local magnitude = math.Clamp(damage / 10, 1, 15)

    -- Electric sparks
    local effectdata = EffectData()
    effectdata:SetOrigin(pos)
    effectdata:SetMagnitude(magnitude)
    effectdata:SetScale(scale)
    util.Effect("Sparks", effectdata)

    -- Smoke for heavy damage
    if damage > 25 then
        effectdata:SetOrigin(pos + Vector(0, 0, 10))
        effectdata:SetMagnitude(3)
        effectdata:SetScale(scale * 0.5)
        util.Effect("smoke_trail", effectdata)
    end

    -- Metal impact sparks for kinetic damage
    if damage > 15 then
        effectdata:SetOrigin(pos)
        effectdata:SetNormal(Vector(0, 0, 1))
        effectdata:SetMagnitude(magnitude)
        effectdata:SetScale(scale)
        util.Effect("MetalSpark", effectdata)
    end

    -- Sound effects based on damage
    local soundLevel = math.Clamp(60 + (damage * 2), 60, 100)
    local pitch = math.random(90, 110)

    if damage > 50 then
        self:EmitSound("ambient/explosions/explode_" .. math.random(1, 9) .. ".wav", soundLevel, pitch)
    elseif damage > 25 then
        self:EmitSound("physics/metal/metal_box_impact_hard" .. math.random(1, 3) .. ".wav", soundLevel, pitch)
    else
        self:EmitSound("ambient/energy/spark" .. math.random(1, 6) .. ".wav", soundLevel, pitch)
    end
end

-- Hull destroyed event
function ENT:OnHullDestroyed()
    print("[ASC Ship Core] Hull destroyed!")

    -- Force decloak
    if self.FlightSystem and self.FlightSystem.cloaking then
        self:DisruptCloaking("Hull destroyed")
    end

    -- Disable systems
    if self.FlightSystem then
        self.FlightSystem.active = false
        self:DeactivateFlightSystems()
    end

    -- Explosion effect
    local effectdata = EffectData()
    effectdata:SetOrigin(self:GetPos())
    effectdata:SetMagnitude(10)
    effectdata:SetScale(2)
    util.Effect("Explosion", effectdata)

    self:EmitSound("ambient/explosions/explode_" .. math.random(1, 9) .. ".wav", 100, 100)
end

concommand.Add("asc_flight_deactivate", function(ply, cmd, args)
    if not IsValid(ply) then return end

    local trace = ply:GetEyeTrace()
    if not IsValid(trace.Entity) or trace.Entity:GetClass() ~= "asc_ship_core" then
        ply:ChatPrint("[ASC Flight] Look at an ASC ship core")
        return
    end

    local core = trace.Entity
    if core.FlightSystem then
        core.FlightSystem.active = false
        core:DeactivateFlightSystems()
        ply:ChatPrint("[ASC Flight] Flight system deactivated")
    else
        ply:ChatPrint("[ASC Flight] Enhanced flight system not available")
    end
end)

concommand.Add("asc_flight_status", function(ply, cmd, args)
    if not IsValid(ply) then return end

    local trace = ply:GetEyeTrace()
    if not IsValid(trace.Entity) or trace.Entity:GetClass() ~= "asc_ship_core" then
        ply:ChatPrint("[ASC Flight] Look at an ASC ship core")
        return
    end

    local core = trace.Entity
    if core.FlightSystem then
        local fs = core.FlightSystem
        ply:ChatPrint("[ASC Flight] === Flight System Status ===")
        ply:ChatPrint("Active: " .. (fs.active and "Yes" or "No"))
        ply:ChatPrint("Mode: " .. fs.mode)
        ply:ChatPrint("Speed: " .. math.Round(fs.velocity:Length()) .. " / " .. math.Round(fs.maxSpeed))
        ply:ChatPrint("Mass: " .. math.Round(fs.mass) .. " kg")
        ply:ChatPrint("Autopilot: " .. (fs.autopilot.active and "Active" or "Inactive"))
        ply:ChatPrint("Waypoints: " .. #fs.autopilot.waypoints)
        ply:ChatPrint("Distance Traveled: " .. math.Round(fs.stats.distanceTraveled) .. " units")
        ply:ChatPrint("Flight Time: " .. math.Round(fs.stats.flightTime) .. " seconds")
    else
        ply:ChatPrint("[ASC Flight] Enhanced flight system not available")
    end
end)

-- Seat control console commands
concommand.Add("asc_seat_control_enable", function(ply, cmd, args)
    if not IsValid(ply) then return end

    local trace = ply:GetEyeTrace()
    if not IsValid(trace.Entity) or trace.Entity:GetClass() ~= "asc_ship_core" then
        ply:ChatPrint("[ASC Flight] Look at an ASC ship core")
        return
    end

    local core = trace.Entity
    if core.FlightSystem and core.FlightSystem.seatControl then
        core.FlightSystem.seatControl.enabled = true
        core:SetNWBool("SeatControlEnabled", true)
        ply:ChatPrint("[ASC Flight] Seat control enabled")
    else
        ply:ChatPrint("[ASC Flight] Enhanced flight system not available")
    end
end)

concommand.Add("asc_seat_control_disable", function(ply, cmd, args)
    if not IsValid(ply) then return end

    local trace = ply:GetEyeTrace()
    if not IsValid(trace.Entity) or trace.Entity:GetClass() ~= "asc_ship_core" then
        ply:ChatPrint("[ASC Flight] Look at an ASC ship core")
        return
    end

    local core = trace.Entity
    if core.FlightSystem and core.FlightSystem.seatControl then
        core.FlightSystem.seatControl.enabled = false
        core:SetNWBool("SeatControlEnabled", false)

        -- Kick current pilot if any
        if IsValid(core.FlightSystem.seatControl.pilot) then
            core:OnPlayerLeftSeat(core.FlightSystem.seatControl.pilot, core.FlightSystem.seatControl.pilotSeat)
        end

        ply:ChatPrint("[ASC Flight] Seat control disabled")
    else
        ply:ChatPrint("[ASC Flight] Enhanced flight system not available")
    end
end)

concommand.Add("asc_seat_detect", function(ply, cmd, args)
    if not IsValid(ply) then return end

    local trace = ply:GetEyeTrace()
    if not IsValid(trace.Entity) or trace.Entity:GetClass() ~= "asc_ship_core" then
        ply:ChatPrint("[ASC Flight] Look at an ASC ship core")
        return
    end

    local core = trace.Entity
    if core.FlightSystem then
        local seats = core:DetectShipSeats()
        ply:ChatPrint("[ASC Flight] Detected " .. #seats .. " seats on ship:")
        for i, seat in ipairs(seats) do
            ply:ChatPrint("  " .. i .. ". " .. seat:GetClass() .. " at " .. tostring(seat:GetPos()))
        end
    else
        ply:ChatPrint("[ASC Flight] Enhanced flight system not available")
    end
end)

concommand.Add("asc_seat_status", function(ply, cmd, args)
    if not IsValid(ply) then return end

    local trace = ply:GetEyeTrace()
    if not IsValid(trace.Entity) or trace.Entity:GetClass() ~= "asc_ship_core" then
        ply:ChatPrint("[ASC Flight] Look at an ASC ship core")
        return
    end

    local core = trace.Entity
    if core.FlightSystem and core.FlightSystem.seatControl then
        local sc = core.FlightSystem.seatControl
        ply:ChatPrint("[ASC Flight] === Seat Control Status ===")
        ply:ChatPrint("Enabled: " .. (sc.enabled and "Yes" or "No"))
        ply:ChatPrint("Available Seats: " .. #(sc.availableSeats or {}))
        ply:ChatPrint("Current Pilot: " .. (IsValid(sc.pilot) and sc.pilot:Name() or "None"))
        ply:ChatPrint("Pilot Seat: " .. (IsValid(sc.pilotSeat) and sc.pilotSeat:GetClass() or "None"))
        ply:ChatPrint("Auto-Activate: " .. (sc.autoActivateOnSit and "Yes" or "No"))
        ply:ChatPrint("Auto-Deactivate: " .. (sc.autoDeactivateOnExit and "Yes" or "No"))
    else
        ply:ChatPrint("[ASC Flight] Enhanced flight system not available")
    end
end)

-- Check for duplicate ship cores on the same ship (supports both classes)
function ENT:CheckForDuplicateShipCores()
    if not IsValid(self) then return end

    local constrainedEntities = constraint.GetAllConstrainedEntities(self)
    local otherShipCores = {}

    if constrainedEntities then
        for ent, _ in pairs(constrainedEntities) do
            if IsValid(ent) and ent ~= self then
                local entClass = ent:GetClass()
                -- Check for both ASC ship cores and legacy ship cores
                if entClass == "asc_ship_core" or entClass == "ship_core" then
                    table.insert(otherShipCores, ent)
                end
            end
        end
    end

    if #otherShipCores > 0 then
        print("[ASC Ship Core] Warning: Found " .. #otherShipCores .. " other ship cores on the same ship")

        -- Notify owner
        local owner = self:CPPIGetOwner()
        if IsValid(owner) and owner:IsPlayer() then
            owner:ChatPrint("[ASC Ship Core] Warning: Multiple ship cores detected. Only one should be used per ship.")
        end

        -- Mark older cores as duplicates (keep the newest one)
        for _, core in ipairs(otherShipCores) do
            if IsValid(core) then
                -- Check if it's an older core (lower EntIndex usually means older)
                if core:EntIndex() < self:EntIndex() then
                    if core.SetStatusMessage then
                        core:SetStatusMessage("DUPLICATE - Remove this core")
                    end
                    core.InvalidDueToDuplicate = true
                    print("[ASC Ship Core] Marked core " .. core:EntIndex() .. " as duplicate")
                end
            end
        end
    end
end

-- Override the Remove function to clean up hooks and timers
local originalRemove = ENT.Remove or function() end
function ENT:Remove()
    -- Clean up welding detection hook
    local hookName = "ASC_ShipCore_WeldDetection_" .. self:EntIndex()
    hook.Remove("OnEntityCreated", hookName)

    -- Clean up auto-weld timer
    local autoWeldTimerName = "ASC_ShipCore_AutoWeld_" .. self:EntIndex()
    timer.Remove(autoWeldTimerName)

    -- Call original remove function
    originalRemove(self)
end

-- Auto-weld system implementation removed

-- All auto-weld functions removed for performance and simplicity

-- Real-time update functions
function ENT:RealTimeEntityScan()
    if not self.ship then return end

    local entities = self.ship:GetEntities()
    self.CachedAttachedEntities = {
        entities = entities,
        count = #entities,
        lastUpdate = CurTime()
    }
end

function ENT:RealTimeResourceUpdate()
    self:UpdateResourceSystem()
end

function ENT:RealTimeSystemCheck()
    self:UpdateSystems()
end

function ENT:RealTimeNetworkSync()
    if WireLib then
        self:UpdateWireOutputs()
    end
    self:UpdateUI()
end

function ENT:UpdateRealTimeData()
    -- Update real-time monitoring data
    if self.ship then
        self:SetNWInt("EntityCount", #self.ship:GetEntities())
        self:SetNWFloat("ShipMass", self.ship.mass or 0)
    end
end

-- Fleet management functions
function ENT:InitializeFleetManagement()
    self.FleetID = 0
    self.FleetRole = "INDEPENDENT"
    print("[ASC Ship Core] Fleet management initialized")
end

function ENT:InitializeRealTimeMonitoring()
    self.RealTimeMonitoring = true
    print("[ASC Ship Core] Real-time monitoring initialized")
end

function ENT:InitializePerformanceAnalytics()
    self.PerformanceMetrics = {}
    print("[ASC Ship Core] Performance analytics initialized")
end

function ENT:StartRealTimeUpdates()
    -- Real-time updates are handled in Think function
    print("[ASC Ship Core] Real-time updates started")
end



-- Optimized entity scanning using advanced optimization system
function ENT:OptimizedEntityScan()
    if not self.ship then return end

    -- Use optimization system if available
    if ASC and ASC.ShipCore and ASC.ShipCore.Optimization then
        local entities = ASC.ShipCore.Optimization.FindEntitiesInRadius(self:GetPos(), 2000)

        -- Queue entities for incremental processing
        local coreId = self:EntIndex()
        if not ASC.ShipCore.Optimization.IncrementalDetection.queues[coreId] then
            ASC.ShipCore.Optimization.IncrementalDetection.queues[coreId] = {}
        end

        -- Add new entities to queue (avoid duplicates)
        local queue = ASC.ShipCore.Optimization.IncrementalDetection.queues[coreId]
        for _, entity in ipairs(entities) do
            if IsValid(entity) and entity ~= self then
                local found = false
                for _, queuedEnt in ipairs(queue) do
                    if queuedEnt == entity then
                        found = true
                        break
                    end
                end
                if not found then
                    table.insert(queue, entity)
                end
            end
        end
    else
        -- Fallback to standard scanning
        self:RealTimeEntityScan()
    end
end

-- Create optimized ship using advanced detection algorithms
function ENT:CreateOptimizedShip()
    if not HYPERDRIVE.ShipCore then return nil end

    -- Use optimization system for initial ship creation
    if ASC and ASC.ShipCore and ASC.ShipCore.Optimization then
        -- Create ship with optimized entity detection
        local ship = HYPERDRIVE.ShipCore.CreateShip(self)

        if ship then
            -- Initialize incremental detection queue for this ship
            local coreId = self:EntIndex()
            ASC.ShipCore.Optimization.IncrementalDetection.queues[coreId] = {}

            -- Mark this core's relationships as dirty for mapping
            ASC.ShipCore.Optimization.RelationshipMap.dirtyEntities[coreId] = true

            print("[ASC Ship Core] Ship created with optimization system - Core ID: " .. coreId)
        end

        return ship
    else
        -- Fallback to standard ship creation
        return HYPERDRIVE.ShipCore.CreateShip(self)
    end
end

-- Optimized constraint checking using cache
function ENT:IsEntityConstrainedToShip(entity)
    if not IsValid(entity) then return false end

    -- Use optimization system if available
    if ASC and ASC.ShipCore and ASC.ShipCore.Optimization then
        return ASC.ShipCore.Optimization.IsEntityPartOfShip(self, entity)
    else
        -- Fallback to standard constraint checking
        if not self.ship or not self.ship.entities then return false end

        local constraints = constraint.GetAllConstrainedEntities(entity)
        if not constraints then return false end

        for constrainedEnt, _ in pairs(constraints) do
            if IsValid(constrainedEnt) then
                if constrainedEnt == self then return true end

                for _, shipEnt in ipairs(self.ship.entities) do
                    if constrainedEnt == shipEnt then return true end
                end
            end
        end

        return false
    end
end

-- CAP technology ambient sound selection - REMOVED per user request

-- Enhanced Power Management System
function ENT:UpdatePowerManagement()
    if not self.PowerManagement then return end

    local pm = self.PowerManagement
    local totalAllocated = 0

    -- Calculate total power allocation with error handling
    for system, data in pairs(pm.powerDistribution or {}) do
        if data and data.active and data.allocated then
            totalAllocated = totalAllocated + (data.allocated or 0)
        end
    end

    -- Ensure valid power values
    pm.availablePower = pm.availablePower or pm.totalPower or 1000
    pm.powerEfficiency = math.max(0.1, math.min(2.0, pm.powerEfficiency or 1.0))

    -- Handle power shortage
    if totalAllocated > pm.availablePower then
        self:HandlePowerShortage()
    end

    -- Update heat generation based on power usage
    pm.heatGeneration = totalAllocated * 0.1 * (2 - pm.powerEfficiency)

    -- Update thermal management
    self:UpdateThermalManagement()

    -- Update subsystem efficiency based on power and heat
    self:UpdateSubsystemEfficiency()
end

function ENT:HandlePowerShortage()
    local pm = self.PowerManagement
    local totalAllocated = 0

    -- Calculate current allocation
    for system, data in pairs(pm.powerDistribution) do
        if data.active then
            totalAllocated = totalAllocated + data.allocated
        end
    end

    if totalAllocated <= pm.availablePower then return end

    -- Sort systems by priority (lower number = higher priority)
    local sortedSystems = {}
    for system, data in pairs(pm.powerDistribution) do
        if data.active then
            table.insert(sortedSystems, {name = system, data = data})
        end
    end

    table.sort(sortedSystems, function(a, b) return a.data.priority < b.data.priority end)

    -- Reduce power to lower priority systems
    local powerToReduce = totalAllocated - pm.availablePower

    for i = #sortedSystems, 1, -1 do
        local system = sortedSystems[i]
        local reduction = math.min(powerToReduce, system.data.allocated * 0.5)
        system.data.allocated = system.data.allocated - reduction
        powerToReduce = powerToReduce - reduction

        if powerToReduce <= 0 then break end
    end

    -- Enter emergency mode if still insufficient power
    if powerToReduce > 0 then
        pm.emergencyMode = true
        self:SetStatusMessage("EMERGENCY: Insufficient power for critical systems!")
    end
end

function ENT:UpdateThermalManagement()
    if not self.ThermalManagement or not self.PowerManagement then return end

    local tm = self.ThermalManagement
    local pm = self.PowerManagement

    -- Calculate heat generation with error handling
    local heatGenerated = pm.heatGeneration or 0

    -- Add heat from damaged subsystems
    if self.SubsystemManagement and self.SubsystemManagement.subsystems then
        for name, subsystem in pairs(self.SubsystemManagement.subsystems) do
            if subsystem and subsystem.health and subsystem.health < 100 then
                heatGenerated = heatGenerated + (100 - subsystem.health) * 0.2
            end
        end
    end

    -- Calculate cooling with safe values
    local coolingEffective = (tm.coolingRate or 5) * (tm.thermalEfficiency or 1.0)

    -- Update temperature with bounds checking
    local tempChange = (heatGenerated - coolingEffective) * 0.1
    tm.coreTemperature = math.max(20, (tm.coreTemperature or 20) + tempChange)

    -- Ensure valid temperature values
    tm.maxTemperature = tm.maxTemperature or 100
    tm.criticalTemperature = tm.criticalTemperature or 150

    -- Handle overheating
    if tm.coreTemperature > tm.maxTemperature then
        tm.overheating = true
        pm.powerEfficiency = math.max(0.5, 1.0 - (tm.coreTemperature - tm.maxTemperature) / 100)

        if tm.coreTemperature > tm.criticalTemperature then
            self:HandleCriticalOverheating()
        end
    else
        tm.overheating = false
        pm.powerEfficiency = math.min(pm.powerEfficiency or 1.0, 1.0)
    end
end

function ENT:HandleCriticalOverheating()
    -- Emergency shutdown of non-critical systems
    local pm = self.PowerManagement

    for system, data in pairs(pm.powerDistribution) do
        if system ~= "lifesupport" and system ~= "shields" then
            data.allocated = data.allocated * 0.5
        end
    end

    self:SetStatusMessage("CRITICAL: Core overheating! Emergency power reduction!")
    self:SetState(4) -- Emergency state
end

function ENT:UpdateSubsystemEfficiency()
    if not self.SubsystemManagement then return end

    local sm = self.SubsystemManagement
    local tm = self.ThermalManagement

    for name, subsystem in pairs(sm.subsystems) do
        -- Calculate efficiency based on health and temperature
        local healthFactor = subsystem.health / 100
        local tempFactor = math.max(0.5, 1.0 - math.max(0, tm.coreTemperature - 50) / 100)

        subsystem.efficiency = healthFactor * tempFactor

        -- Auto-repair if enabled
        if sm.autoRepair and subsystem.health < 100 then
            subsystem.health = math.min(100, subsystem.health + sm.repairRate * 0.1)
        end
    end
end

function ENT:SetPowerAllocation(system, amount)
    if not self.PowerManagement or not self.PowerManagement.powerDistribution[system] then
        return false
    end

    self.PowerManagement.powerDistribution[system].allocated = math.max(0, amount)
    self:UpdatePowerManagement()
    return true
end

function ENT:GetPowerAllocation(system)
    if not self.PowerManagement or not self.PowerManagement.powerDistribution[system] then
        return 0
    end

    return self.PowerManagement.powerDistribution[system].allocated
end

function ENT:SetSystemPriority(system, priority)
    if not self.PowerManagement or not self.PowerManagement.powerDistribution[system] then
        return false
    end

    self.PowerManagement.powerDistribution[system].priority = priority
    return true
end

function ENT:GetSystemEfficiency(system)
    if not self.SubsystemManagement or not self.SubsystemManagement.subsystems[system] then
        return 1.0
    end

    return self.SubsystemManagement.subsystems[system].efficiency
end

function ENT:DamageSubsystem(system, damage)
    if not self.SubsystemManagement or not self.SubsystemManagement.subsystems[system] then
        return false
    end

    local subsystem = self.SubsystemManagement.subsystems[system]
    subsystem.health = math.max(0, subsystem.health - damage)

    if subsystem.health <= 0 and subsystem.critical then
        self:HandleCriticalSystemFailure(system)
    end

    return true
end

function ENT:HandleCriticalSystemFailure(system)
    if system == "reactor" then
        self:SetState(4) -- Emergency
        self:SetStatusMessage("CRITICAL: Reactor failure! Emergency protocols activated!")
    elseif system == "powerGrid" then
        if self.PowerManagement and self.PowerManagement.availablePower then
            self.PowerManagement.availablePower = self.PowerManagement.availablePower * 0.5
        end
        self:SetStatusMessage("CRITICAL: Power grid failure! Power reduced!")
    elseif system == "lifesupport" then
        self:SetStatusMessage("CRITICAL: Life support failure! Seek immediate assistance!")
    end
end

-- Crew Efficiency System (inspired by FTL and Star Citizen)
function ENT:UpdateCrewEfficiency()
    if not self.ship then return end

    -- Initialize crew efficiency system if not exists
    if not self.CrewEfficiency then
        self.CrewEfficiency = {
            totalCrew = 0,
            skillLevels = {},
            systemBonuses = {},
            overallEfficiency = 1.0,
            experienceGain = 0.1
        }
    end

    local crew = self.CrewEfficiency
    local playersOnShip = {}

    -- Find players on ship
    for _, ply in ipairs(player.GetAll()) do
        if IsValid(ply) and self:IsPlayerOnShip(ply) then
            table.insert(playersOnShip, ply)
        end
    end

    crew.totalCrew = #playersOnShip

    -- Calculate crew bonuses based on player skills and presence
    if crew.totalCrew > 0 then
        -- Engineering bonus (improves power efficiency)
        local engineeringBonus = math.min(1.5, 1.0 + (crew.totalCrew * 0.1))
        if self.PowerManagement and self.PowerManagement.powerEfficiency then
            self.PowerManagement.powerEfficiency = self.PowerManagement.powerEfficiency * engineeringBonus
        end

        -- Repair bonus (improves subsystem repair rate)
        local repairBonus = math.min(2.0, 1.0 + (crew.totalCrew * 0.15))
        if self.SubsystemManagement and self.SubsystemManagement.repairRate then
            self.SubsystemManagement.repairRate = self.SubsystemManagement.repairRate * repairBonus
        end

        -- Tactical bonus (improves system response time)
        local tacticalBonus = math.min(1.3, 1.0 + (crew.totalCrew * 0.05))

        -- Store bonuses for UI display
        crew.systemBonuses = {
            engineering = engineeringBonus,
            repair = repairBonus,
            tactical = tacticalBonus
        }

        crew.overallEfficiency = (engineeringBonus + repairBonus + tacticalBonus) / 3
    else
        -- No crew penalties
        crew.systemBonuses = {
            engineering = 0.8, -- Reduced efficiency without crew
            repair = 0.5,      -- Slower repairs
            tactical = 0.7     -- Slower response
        }
        crew.overallEfficiency = 0.67
    end

    -- Update network variables for UI
    self:SetNWInt("CrewCount", crew.totalCrew)
    self:SetNWFloat("CrewEfficiency", crew.overallEfficiency)
end

function ENT:IsPlayerOnShip(ply)
    if not IsValid(ply) or not self.ship then return false end

    local playerPos = ply:GetPos()
    local shipCenter = self.ship:GetCenter()

    -- Calculate ship radius from entities if GetRadius doesn't exist
    local shipRadius = 1000 -- Default radius
    if self.ship.GetRadius then
        shipRadius = self.ship:GetRadius()
    elseif self.ship.GetEntities then
        -- Calculate radius from ship entities
        local entities = self.ship:GetEntities()
        if entities and #entities > 0 then
            local maxDist = 0
            for _, ent in ipairs(entities) do
                if IsValid(ent) then
                    local dist = shipCenter:Distance(ent:GetPos())
                    if dist > maxDist then
                        maxDist = dist
                    end
                end
            end
            shipRadius = math.max(500, maxDist + 200) -- Add buffer
        end
    end

    -- Check if player is within ship bounds
    if playerPos:Distance(shipCenter) > shipRadius then return false end

    -- Check if player is on a ship entity
    local standingOn = ply:GetGroundEntity()
    if IsValid(standingOn) then
        if self.ship.GetEntities then
            for _, shipEnt in ipairs(self.ship:GetEntities()) do
                if standingOn == shipEnt then
                    return true
                end
            end
        end
    end

    -- Alternative check: if player is close to ship core
    if playerPos:Distance(self:GetPos()) < 500 then
        return true
    end

    return false
end

-- Advanced Resource Management with Power Dependencies
function ENT:UpdateBasicResourceRegeneration()
    if not self.BasicResourceStorage then return end

    local powerEfficiency = self.PowerManagement and self.PowerManagement.powerEfficiency or 1.0
    local crewEfficiency = self.CrewEfficiency and self.CrewEfficiency.overallEfficiency or 1.0

    for resourceType, resource in pairs(self.BasicResourceStorage) do
        if resource.regenRate > 0 then
            -- Apply power and crew efficiency to regeneration
            local effectiveRegenRate = resource.regenRate * powerEfficiency * crewEfficiency

            -- Reduce regeneration if systems are damaged
            if self.SubsystemManagement then
                local systemHealth = self.SubsystemManagement.subsystems.lifesupport and
                                   self.SubsystemManagement.subsystems.lifesupport.health or 100
                effectiveRegenRate = effectiveRegenRate * (systemHealth / 100)
            end

            resource.amount = math.min(resource.capacity, resource.amount + effectiveRegenRate)
        end

        -- Handle critical resource shortages
        if resource.critical and resource.amount < resource.capacity * 0.1 then
            self:HandleCriticalResourceShortage(resourceType)
        end
    end
end

function ENT:HandleCriticalResourceShortage(resourceType)
    if resourceType == "energy" then
        self:SetStatusMessage("CRITICAL: Energy reserves depleted!")
        if self.PowerManagement and self.PowerManagement.availablePower then
            self.PowerManagement.availablePower = self.PowerManagement.availablePower * 0.7
        end
    elseif resourceType == "oxygen" then
        self:SetStatusMessage("CRITICAL: Oxygen shortage detected!")
        -- Damage life support system
        if self.SubsystemManagement and self.SubsystemManagement.subsystems and
           self.SubsystemManagement.subsystems.lifesupport and
           self.SubsystemManagement.subsystems.lifesupport.health then
            self.SubsystemManagement.subsystems.lifesupport.health =
                self.SubsystemManagement.subsystems.lifesupport.health - 5
        end
    elseif resourceType == "coolant" then
        self:SetStatusMessage("CRITICAL: Coolant shortage! Overheating risk!")
        if self.ThermalManagement and self.ThermalManagement.coolingRate then
            self.ThermalManagement.coolingRate = self.ThermalManagement.coolingRate * 0.5
        end
    end
end

-- Console commands for model selection
concommand.Add("aria_ship_core_next_model", function(ply, cmd, args)
    if not IsValid(ply) then return end

    local core = ply:GetEyeTrace().Entity
    if IsValid(core) and core:GetClass() == "asc_ship_core" then
        core:NextModel()
        local modelName = core:GetNWString("SelectedModelName", "Unknown")
        ply:ChatPrint("[ASC Ship Core] Model changed to: " .. modelName)
    else
        ply:ChatPrint("[ASC Ship Core] Look at a ship core to change its model")
    end
end, nil, "Change ship core to next model")

concommand.Add("aria_ship_core_prev_model", function(ply, cmd, args)
    if not IsValid(ply) then return end

    local core = ply:GetEyeTrace().Entity
    if IsValid(core) and core:GetClass() == "asc_ship_core" then
        core:PreviousModel()
        local modelName = core:GetNWString("SelectedModelName", "Unknown")
        ply:ChatPrint("[ASC Ship Core] Model changed to: " .. modelName)
    else
        ply:ChatPrint("[ASC Ship Core] Look at a ship core to change its model")
    end
end, nil, "Change ship core to previous model")

concommand.Add("aria_ship_core_set_model", function(ply, cmd, args)
    if not IsValid(ply) then return end
    if #args < 1 then
        ply:ChatPrint("[ASC Ship Core] Usage: aria_ship_core_set_model <model_index>")
        return
    end

    local index = tonumber(args[1])
    if not index then
        ply:ChatPrint("[ASC Ship Core] Invalid model index")
        return
    end

    local core = ply:GetEyeTrace().Entity
    if IsValid(core) and core:GetClass() == "asc_ship_core" then
        if core:SetModelByIndex(index) then
            local modelName = core:GetNWString("SelectedModelName", "Unknown")
            ply:ChatPrint("[ASC Ship Core] Model changed to: " .. modelName)
        else
            ply:ChatPrint("[ASC Ship Core] Invalid model index: " .. index)
        end
    else
        ply:ChatPrint("[ASC Ship Core] Look at a ship core to change its model")
    end
end, nil, "Set ship core model by index")

concommand.Add("aria_ship_core_list_models", function(ply, cmd, args)
    if not IsValid(ply) then return end

    local core = ply:GetEyeTrace().Entity
    if IsValid(core) and core:GetClass() == "asc_ship_core" then
        local info = core:GetModelInfo()
        ply:ChatPrint("[ASC Ship Core] Available models (" .. info.totalModels .. " total):")

        for _, modelData in ipairs(info.models) do
            local prefix = modelData.selected and ">>> " or "    "
            ply:ChatPrint(prefix .. modelData.index .. ". " .. modelData.name .. " (" .. modelData.category .. ")")
        end
    else
        ply:ChatPrint("[ASC Ship Core] Look at a ship core to list its models")
    end
end, nil, "List available ship core models")

-- Performance optimization functions
function ENT:GetAdaptiveThinkRate()
    -- Check performance every few seconds
    local currentTime = CurTime()
    if currentTime - self.LastPerformanceCheck > self.PerformanceCheckInterval then
        self:CheckPerformance()
        self.LastPerformanceCheck = currentTime
    end

    -- Adaptive think rates based on performance
    if self.PerformanceMode then
        return 0.1 -- 10 FPS in performance mode
    else
        local fps = self:GetCurrentFPS()
        if fps < 30 then
            return 0.05 -- 20 FPS for low performance
        elseif fps < 45 then
            return 0.033 -- 30 FPS for medium performance
        else
            return 0.02 -- 50 FPS for good performance
        end
    end
end

function ENT:CheckPerformance()
    local frameTime = FrameTime()

    -- Track frame time history
    table.insert(self.FrameTimeHistory, frameTime)
    if #self.FrameTimeHistory > self.MaxFrameHistory then
        table.remove(self.FrameTimeHistory, 1)
    end

    -- Calculate average FPS
    local avgFrameTime = 0
    for _, ft in ipairs(self.FrameTimeHistory) do
        avgFrameTime = avgFrameTime + ft
    end
    avgFrameTime = avgFrameTime / #self.FrameTimeHistory

    local avgFPS = 1 / avgFrameTime

    -- Enable performance mode if FPS is consistently low
    if avgFPS < 25 and not self.PerformanceMode then
        self:EnablePerformanceMode()
    elseif avgFPS > 40 and self.PerformanceMode then
        self:DisablePerformanceMode()
    end
end

function ENT:GetCurrentFPS()
    if #self.FrameTimeHistory > 0 then
        local avgFrameTime = 0
        for _, ft in ipairs(self.FrameTimeHistory) do
            avgFrameTime = avgFrameTime + ft
        end
        avgFrameTime = avgFrameTime / #self.FrameTimeHistory
        return 1 / avgFrameTime
    end
    return 60 -- Default assumption
end

function ENT:EnablePerformanceMode()
    if self.PerformanceMode then return end

    self.PerformanceMode = true

    -- Reduce update frequencies even further for performance mode
    self.EntityScanRate = 5.0 -- 0.2 FPS
    self.ResourceUpdateRate = 3.0 -- 0.33 FPS
    self.SystemCheckRate = 10.0 -- 0.1 FPS
    self.NetworkUpdateRate = 2.0 -- 0.5 FPS

    -- Auto-weld system removed

    print("[ASC Ship Core] Performance mode enabled - reduced update rates")

    -- Notify owner
    local owner = self.CPPIGetOwner and self:CPPIGetOwner() or nil
    if IsValid(owner) then
        owner:ChatPrint("[ASC Ship Core] Performance mode enabled due to low FPS")
    end
end

function ENT:DisablePerformanceMode()
    if not self.PerformanceMode then return end

    self.PerformanceMode = false

    -- Restore optimized update frequencies (still reduced from original for better performance)
    self.EntityScanRate = 2.0 -- 0.5 FPS
    self.ResourceUpdateRate = 1.0 -- 1 FPS
    self.SystemCheckRate = 3.0 -- 0.33 FPS
    self.NetworkUpdateRate = 1.0 -- 1 FPS

    print("[ASC Ship Core] Performance mode disabled - restored optimized update rates")

    -- Notify owner
    local owner = self.CPPIGetOwner and self:CPPIGetOwner() or nil
    if IsValid(owner) then
        owner:ChatPrint("[ASC Ship Core] Performance mode disabled - using optimized rates")
    end
end

-- Console commands for testing hull and combat systems
concommand.Add("asc_test_hull_damage", function(ply, cmd, args)
    if not IsValid(ply) then return end

    local trace = ply:GetEyeTrace()
    if not IsValid(trace.Entity) or trace.Entity:GetClass() ~= "asc_ship_core" then
        ply:ChatPrint("[ASC Hull] Look at an ASC ship core")
        return
    end

    local core = trace.Entity
    local damage = tonumber(args[1]) or 50

    if core.HullSystem then
        -- Create fake damage info
        local dmginfo = DamageInfo()
        dmginfo:SetDamage(damage)
        dmginfo:SetAttacker(ply)
        dmginfo:SetDamageType(DMG_BULLET)
        dmginfo:SetDamagePosition(core:GetPos())

        core:OnTakeDamage(dmginfo)

        local integrity = (core.HullSystem.currentHull / core.HullSystem.maxHull) * 100
        ply:ChatPrint("[ASC Hull] Applied " .. damage .. " damage. Hull integrity: " .. math.Round(integrity) .. "%")
    else
        ply:ChatPrint("[ASC Hull] Hull system not initialized")
    end
end)

concommand.Add("asc_repair_hull", function(ply, cmd, args)
    if not IsValid(ply) then return end

    local trace = ply:GetEyeTrace()
    if not IsValid(trace.Entity) or trace.Entity:GetClass() ~= "asc_ship_core" then
        ply:ChatPrint("[ASC Hull] Look at an ASC ship core")
        return
    end

    local core = trace.Entity
    local repairAmount = tonumber(args[1]) or 100

    if core.HullSystem then
        core.HullSystem.currentHull = math.min(core.HullSystem.maxHull, core.HullSystem.currentHull + repairAmount)

        local integrity = (core.HullSystem.currentHull / core.HullSystem.maxHull) * 100
        ply:ChatPrint("[ASC Hull] Repaired " .. repairAmount .. " hull. Hull integrity: " .. math.Round(integrity) .. "%")
    else
        ply:ChatPrint("[ASC Hull] Hull system not initialized")
    end
end)

concommand.Add("asc_test_combat", function(ply, cmd, args)
    if not IsValid(ply) then return end

    local trace = ply:GetEyeTrace()
    if not IsValid(trace.Entity) or trace.Entity:GetClass() ~= "asc_ship_core" then
        ply:ChatPrint("[ASC Combat] Look at an ASC ship core")
        return
    end

    local core = trace.Entity

    if core.CombatSystem then
        ply:ChatPrint("[ASC Combat] Combat System Status:")
        ply:ChatPrint("  Mode: " .. core.CombatSystem.combatMode)
        ply:ChatPrint("  Alert Level: " .. core.CombatSystem.alertLevel)
        ply:ChatPrint("  Weapons: " .. #core.CombatSystem.weapons)
        ply:ChatPrint("  Defenses: " .. #core.CombatSystem.defenses)
        ply:ChatPrint("  Targets: " .. #core.CombatSystem.targetingSystem.targets)
        ply:ChatPrint("  Current Target: " .. tostring(core.CombatSystem.targetingSystem.currentTarget))
        ply:ChatPrint("  Combat Efficiency: " .. math.Round(core.CombatSystem.combatEfficiency * 100) .. "%")
        ply:ChatPrint("  Damage Dealt: " .. math.Round(core.CombatSystem.totalDamageDealt))
        ply:ChatPrint("  Damage Received: " .. math.Round(core.CombatSystem.totalDamageReceived))
    else
        ply:ChatPrint("[ASC Combat] Combat system not initialized")
    end
end)

concommand.Add("asc_hull_status", function(ply, cmd, args)
    if not IsValid(ply) then return end

    local trace = ply:GetEyeTrace()
    if not IsValid(trace.Entity) or trace.Entity:GetClass() ~= "asc_ship_core" then
        ply:ChatPrint("[ASC Hull] Look at an ASC ship core")
        return
    end

    local core = trace.Entity

    if core.HullSystem then
        local hull = core.HullSystem
        local integrity = (hull.currentHull / hull.maxHull) * 100

        ply:ChatPrint("[ASC Hull] Hull System Status:")
        ply:ChatPrint("  Integrity: " .. math.Round(integrity) .. "% (" .. math.Round(hull.currentHull) .. "/" .. hull.maxHull .. ")")
        ply:ChatPrint("  Critical Mode: " .. (hull.criticalMode and "YES" or "NO"))
        ply:ChatPrint("  Emergency Mode: " .. (hull.emergencyMode and "YES" or "NO"))
        ply:ChatPrint("  Auto Repair: " .. (hull.autoRepair and "ENABLED" or "DISABLED"))
        ply:ChatPrint("  Repair Active: " .. (hull.autoRepairActive and "YES" or "NO"))
        ply:ChatPrint("  Repair Rate: " .. hull.repairRate .. " HP/sec")
        ply:ChatPrint("  Armor Rating: " .. hull.armorRating)
        ply:ChatPrint("  Total Damage Received: " .. math.Round(hull.totalDamageReceived))
        ply:ChatPrint("  Damage Rate: " .. math.Round(hull.damagePerSecond) .. " DPS")
        ply:ChatPrint("  Hull Breaches: " .. #hull.breaches)
        ply:ChatPrint("  System Failures: " .. #hull.systemFailures)
    else
        ply:ChatPrint("[ASC Hull] Hull system not initialized")
    end
end)

print("[ASC Ship Core] Enhanced ship core with ALL features including built-in hull damage and combat systems loaded")
