--[[
    Advanced Space Combat - Base Weapon Entity
    
    This is the base class for all ASC weapons, providing:
    - Common weapon functionality
    - Targeting system integration
    - Energy management
    - Heat management
    - Ammunition system
    - Auto-fire capabilities
    - Ship core integration
]]

AddCSLuaFile("cl_init.lua")
AddCSLuaFile("shared.lua")
include("shared.lua")

-- Initialize weapon
function ENT:Initialize()
    -- Basic entity setup
    self:SetModel(self.WeaponModel or "models/props_combine/combine_interface001.mdl")
    self:PhysicsInit(SOLID_VPHYSICS)
    self:SetMoveType(MOVETYPE_VPHYSICS)
    self:SetSolid(SOLID_VPHYSICS)
    self:SetUseType(SIMPLE_USE)
    
    local phys = self:GetPhysicsObject()
    if IsValid(phys) then
        phys:Wake()
        phys:SetMass(self.WeaponMass or 100)
    end
    
    -- Weapon properties (can be overridden by child classes)
    self.WeaponName = self.WeaponName or "Base Weapon"
    self.WeaponType = self.WeaponType or "energy"
    self.Damage = self.Damage or 100
    self.Range = self.Range or 2000
    self.FireRate = self.FireRate or 1.0 -- shots per second
    self.EnergyConsumption = self.EnergyConsumption or 25
    self.ProjectileSpeed = self.ProjectileSpeed or 1000
    self.ProjectileType = self.ProjectileType or "energy_bolt"
    self.Accuracy = self.Accuracy or 0.9
    
    -- Heat management
    self.MaxHeat = self.MaxHeat or 100
    self.HeatPerShot = self.HeatPerShot or 10
    self.CooldownRate = self.CooldownRate or 20 -- heat units per second
    self.OverheatThreshold = self.OverheatThreshold or 90
    
    -- Ammunition system
    self.MaxAmmo = self.MaxAmmo or -1 -- -1 = unlimited
    self.AmmoType = self.AmmoType or "energy"
    self.AmmoRegenRate = self.AmmoRegenRate or 0 -- ammo per second
    
    -- State variables
    self.CurrentHeat = 0
    self.CurrentAmmo = self.MaxAmmo
    self.LastFired = 0
    self.Overheated = false
    self.Active = true
    self.AutoFire = false
    self.CurrentTarget = nil
    
    -- Ship integration
    self.ShipCore = nil
    self.WeaponGroup = nil
    self.WeaponID = nil
    
    -- Statistics
    self.TotalShots = 0
    self.TotalDamage = 0
    self.TotalHits = 0
    
    -- Networking
    self:SetNWBool("Active", true)
    self:SetNWBool("AutoFire", false)
    self:SetNWBool("Overheated", false)
    self:SetNWFloat("Heat", 0)
    self:SetNWFloat("Ammo", self.CurrentAmmo)
    self:SetNWString("WeaponName", self.WeaponName)
    self:SetNWString("WeaponType", self.WeaponType)
    
    -- Find ship core
    timer.Simple(1, function()
        if IsValid(self) then
            self:FindShipCore()
        end
    end)
    
    print("[ASC Weapon] " .. self.WeaponName .. " initialized")
end

-- Find and link to ship core
function ENT:FindShipCore()
    -- Check if we're constrained to a ship core
    local constrainedEntities = constraint.GetAllConstrainedEntities(self)
    if constrainedEntities then
        for ent, _ in pairs(constrainedEntities) do
            if IsValid(ent) and (ent:GetClass() == "asc_ship_core" or ent:GetClass() == "ship_core") then
                self:LinkToShipCore(ent)
                return
            end
        end
    end
    
    -- Look for nearby ship cores
    local nearbyEnts = ents.FindInSphere(self:GetPos(), 2000)
    for _, ent in ipairs(nearbyEnts) do
        if IsValid(ent) and (ent:GetClass() == "asc_ship_core" or ent:GetClass() == "ship_core") then
            -- Check if this weapon is part of the ship core's ship
            if ent.ship and ent.ship.entities then
                for _, shipEnt in ipairs(ent.ship.entities) do
                    if shipEnt == self then
                        self:LinkToShipCore(ent)
                        return
                    end
                end
            end
        end
    end
end

-- Link to ship core
function ENT:LinkToShipCore(shipCore)
    if not IsValid(shipCore) then return end
    
    self.ShipCore = shipCore
    print("[ASC Weapon] " .. self.WeaponName .. " linked to ship core: " .. tostring(shipCore))
    
    -- Register with weapon system if available
    if ASC.Weapons and ASC.Weapons.Core then
        local shipID = tostring(shipCore:EntIndex())
        local weaponSystem = ASC.Weapons.Core.WeaponSystems[shipID]
        
        if not weaponSystem then
            ASC.Weapons.Core.CreateWeaponSystem(shipCore)
            weaponSystem = ASC.Weapons.Core.WeaponSystems[shipID]
        end
        
        if weaponSystem then
            -- Add this weapon to the system
            local weapon = ASC.Weapons.Core.AddWeapon(shipID, self.WeaponType, self:GetPos(), self:GetAngles())
            if weapon then
                weapon.entity = self
                self.WeaponID = weapon.id
                print("[ASC Weapon] Registered with weapon system as ID: " .. weapon.id)
            end
        end
    end
end

-- Main think function
function ENT:Think()
    local currentTime = CurTime()
    
    -- Heat management
    self:UpdateHeat()
    
    -- Ammunition regeneration
    self:UpdateAmmo()
    
    -- Auto-fire system
    if self.AutoFire and self.Active and not self.Overheated then
        self:UpdateAutoFire()
    end
    
    -- Update networking
    self:UpdateNetworking()
    
    self:NextThink(currentTime + 0.1) -- 10 FPS update rate
    return true
end

-- Update heat management
function ENT:UpdateHeat()
    if self.CurrentHeat > 0 then
        self.CurrentHeat = math.max(0, self.CurrentHeat - self.CooldownRate * 0.1) -- 0.1 = think interval
        
        if self.Overheated and self.CurrentHeat < self.OverheatThreshold * 0.5 then
            self.Overheated = false
            self:OnCooldown()
        end
    end
end

-- Update ammunition
function ENT:UpdateAmmo()
    if self.MaxAmmo > 0 and self.CurrentAmmo < self.MaxAmmo and self.AmmoRegenRate > 0 then
        self.CurrentAmmo = math.min(self.MaxAmmo, self.CurrentAmmo + self.AmmoRegenRate * 0.1) -- 0.1 = think interval
    end
end

-- Update auto-fire system
function ENT:UpdateAutoFire()
    if not self.CurrentTarget or not IsValid(self.CurrentTarget) then
        self:FindTarget()
    end
    
    if IsValid(self.CurrentTarget) then
        local distance = self:GetPos():Distance(self.CurrentTarget:GetPos())
        if distance <= self.Range then
            self:Fire(self.CurrentTarget)
        else
            self.CurrentTarget = nil -- Target out of range
        end
    end
end

-- Update networking
function ENT:UpdateNetworking()
    self:SetNWBool("Active", self.Active)
    self:SetNWBool("AutoFire", self.AutoFire)
    self:SetNWBool("Overheated", self.Overheated)
    self:SetNWFloat("Heat", self.CurrentHeat / self.MaxHeat)
    self:SetNWFloat("Ammo", self.CurrentAmmo)
    self:SetNWEntity("Target", self.CurrentTarget)
end

-- Fire weapon (to be overridden by child classes)
function ENT:Fire(target)
    if not self:CanFire() then return false end
    
    local currentTime = CurTime()
    local cooldown = 1 / self.FireRate
    
    if currentTime - self.LastFired < cooldown then return false end
    
    -- Consume energy from ship core
    if IsValid(self.ShipCore) and not self:ConsumeEnergy(self.EnergyConsumption) then
        return false
    end
    
    -- Consume ammunition
    if not self:ConsumeAmmo(1) then return false end
    
    -- Generate heat
    self.CurrentHeat = math.min(self.MaxHeat, self.CurrentHeat + self.HeatPerShot)
    if self.CurrentHeat >= self.OverheatThreshold then
        self.Overheated = true
        self:OnOverheat()
    end
    
    -- Create projectile (to be implemented by child classes)
    local success = self:CreateProjectile(target)
    
    if success then
        self.LastFired = currentTime
        self.TotalShots = self.TotalShots + 1
        
        -- Effects
        self:OnFire(target)
        
        return true
    end
    
    return false
end

-- Check if weapon can fire
function ENT:CanFire()
    return self.Active and not self.Overheated and (self.MaxAmmo < 0 or self.CurrentAmmo > 0)
end

-- Consume energy from ship core
function ENT:ConsumeEnergy(amount)
    if not IsValid(self.ShipCore) then return true end -- No ship core = unlimited energy
    
    if self.ShipCore.ConsumeEnergy then
        return self.ShipCore:ConsumeEnergy(amount)
    elseif self.ShipCore.ResourceSystem then
        if self.ShipCore.ResourceSystem.energy >= amount then
            self.ShipCore.ResourceSystem.energy = self.ShipCore.ResourceSystem.energy - amount
            return true
        end
    end
    
    return false
end

-- Consume ammunition
function ENT:ConsumeAmmo(amount)
    if self.MaxAmmo < 0 then return true end -- Unlimited ammo
    
    if self.CurrentAmmo >= amount then
        self.CurrentAmmo = self.CurrentAmmo - amount
        return true
    end
    
    return false
end

-- Create projectile (to be overridden by child classes)
function ENT:CreateProjectile(target)
    -- Default implementation - child classes should override this
    return false
end

-- Find target for auto-fire
function ENT:FindTarget()
    if not self.AutoFire then return end
    
    local myPos = self:GetPos()
    local potentialTargets = {}
    
    -- Find entities in range
    local nearbyEnts = ents.FindInSphere(myPos, self.Range)
    for _, ent in ipairs(nearbyEnts) do
        if self:IsValidTarget(ent) then
            local distance = myPos:Distance(ent:GetPos())
            local priority = self:CalculateTargetPriority(ent, distance)
            
            table.insert(potentialTargets, {
                entity = ent,
                distance = distance,
                priority = priority
            })
        end
    end
    
    -- Sort by priority
    table.sort(potentialTargets, function(a, b)
        return a.priority > b.priority
    end)
    
    -- Select best target
    if #potentialTargets > 0 then
        self.CurrentTarget = potentialTargets[1].entity
    end
end

-- Check if entity is a valid target
function ENT:IsValidTarget(ent)
    if not IsValid(ent) then return false end
    if ent == self then return false end
    if ent == self.ShipCore then return false end
    
    -- Check if it's part of our ship
    if self.ShipCore and self.ShipCore.ship and self.ShipCore.ship.entities then
        for _, shipEnt in ipairs(self.ShipCore.ship.entities) do
            if ent == shipEnt then return false end
        end
    end
    
    -- Check friendly fire
    local owner = self:GetOwner()
    if IsValid(owner) and ent:GetOwner() == owner then return false end
    
    -- Check target types
    local class = ent:GetClass()
    if string.find(class, "ship_core") or string.find(class, "weapon") or 
       string.find(class, "engine") or ent:IsPlayer() or ent:IsNPC() then
        return true
    end
    
    return false
end

-- Calculate target priority
function ENT:CalculateTargetPriority(ent, distance)
    local priority = 0
    local class = ent:GetClass()
    
    -- Base priority by type
    if string.find(class, "ship_core") then
        priority = 100
    elseif string.find(class, "weapon") then
        priority = 80
    elseif ent:IsPlayer() then
        priority = 70
    elseif ent:IsNPC() then
        priority = 50
    else
        priority = 30
    end
    
    -- Distance factor
    local distanceFactor = (self.Range - distance) / self.Range
    priority = priority * distanceFactor
    
    return priority
end

-- Event handlers (to be overridden by child classes)
function ENT:OnFire(target)
    -- Default fire effects
    self:EmitSound("weapons/physcannon/energy_sing_loop4.wav", 75, 100)
end

function ENT:OnOverheat()
    self:EmitSound("ambient/energy/spark6.wav", 80, 60)
    print("[ASC Weapon] " .. self.WeaponName .. " overheated!")
end

function ENT:OnCooldown()
    self:EmitSound("ambient/energy/weld2.wav", 60, 120)
    print("[ASC Weapon] " .. self.WeaponName .. " cooled down")
end

-- Use function
function ENT:Use(activator, caller)
    if IsValid(activator) and activator:IsPlayer() then
        self.AutoFire = not self.AutoFire
        activator:ChatPrint("[ASC Weapon] " .. self.WeaponName .. " auto-fire: " .. (self.AutoFire and "ON" or "OFF"))
    end
end

-- Damage handling
function ENT:OnTakeDamage(dmginfo)
    local damage = dmginfo:GetDamage()
    local health = self:Health() - damage
    
    self:SetHealth(health)
    
    if health <= 0 then
        self:Explode()
    end
end

-- Explode when destroyed
function ENT:Explode()
    local effectdata = EffectData()
    effectdata:SetOrigin(self:GetPos())
    effectdata:SetMagnitude(2)
    util.Effect("Explosion", effectdata)
    
    self:EmitSound("ambient/explosions/explode_4.wav", 100, 100)
    self:Remove()
end

-- Get weapon status
function ENT:GetWeaponStatus()
    return {
        name = self.WeaponName,
        type = self.WeaponType,
        active = self.Active,
        autoFire = self.AutoFire,
        overheated = self.Overheated,
        heat = self.CurrentHeat / self.MaxHeat,
        ammo = self.CurrentAmmo,
        maxAmmo = self.MaxAmmo,
        target = self.CurrentTarget,
        totalShots = self.TotalShots,
        totalDamage = self.TotalDamage,
        totalHits = self.TotalHits
    }
end
