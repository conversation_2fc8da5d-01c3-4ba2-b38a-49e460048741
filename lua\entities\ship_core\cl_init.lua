-- Ship Core Compatibility Entity - Client Side

include("shared.lua")

function ENT:Initialize()
    -- Set up visual indicators that this is redirecting
    self.NextFlash = 0
    self.FlashState = false
end

function ENT:Draw()
    -- Draw the entity with a flashing effect to show it's redirecting
    local time = CurTime()
    
    if time > self.NextFlash then
        self.FlashState = not self.FlashState
        self.NextFlash = time + 0.5
    end
    
    -- Set color based on flash state
    if self.FlashState then
        self:SetColor(Color(100, 150, 255, 255))  -- Bright blue
    else
        self:SetColor(Color(100, 150, 255, 150))  -- Dimmer blue
    end
    
    self:DrawModel()
    
    -- Draw text above the entity
    local pos = self:GetPos() + Vector(0, 0, 50)
    local ang = LocalPlayer():EyeAngles()
    ang:RotateAroundAxis(ang:Forward(), 90)
    ang:RotateAroundAxis(ang:Right(), 90)
    
    cam.Start3D2D(pos, ang, 0.1)
        draw.SimpleTextOutlined("UPGRADING TO ASC SHIP CORE", "DermaLarge", 0, 0, Color(255, 255, 255), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER, 2, Color(0, 0, 0))
        draw.SimpleTextOutlined("Please wait...", "DermaDefault", 0, 30, Color(200, 200, 200), TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER, 1, Color(0, 0, 0))
    cam.End3D2D()
end

function ENT:Think()
    -- Add some particle effects to show it's working
    if math.random(1, 10) == 1 then
        local effectdata = EffectData()
        effectdata:SetOrigin(self:GetPos() + Vector(math.random(-20, 20), math.random(-20, 20), math.random(0, 40)))
        effectdata:SetMagnitude(1)
        effectdata:SetScale(0.5)
        util.Effect("Sparks", effectdata)
    end
    
    self:NextThink(CurTime() + 0.1)
    return true
end

-- Override GetClass for client-side compatibility
function ENT:GetClass()
    return "ship_core"  -- Return legacy class name for client compatibility
end
