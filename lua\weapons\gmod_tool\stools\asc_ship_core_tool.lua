-- Advanced Space Combat - Ship Core Tool
-- Comprehensive ship core spawning and management tool

TOOL.Category = "Advanced Space Combat"
TOOL.Name = "ASC Ship Core Tool v6.0.0"
TOOL.Command = nil
TOOL.ConfigName = ""

-- Client ConVars
TOOL.ClientConVar["ship_name"] = "New Ship"
TOOL.ClientConVar["auto_link"] = "1"
TOOL.ClientConVar["power_level"] = "100"
TOOL.ClientConVar["enable_life_support"] = "1"
TOOL.ClientConVar["enable_shields"] = "1"
TOOL.ClientConVar["enable_hull_system"] = "1"

if CLIENT then
    -- Add language strings with safe access
    local success, result = pcall(function()
        if _G.language and _G.language.Add then
            _G.language.Add("tool.asc_ship_core_tool.name", "ASC Ship Core Tool v6.0.0")
            _G.language.Add("tool.asc_ship_core_tool.desc", "Spawn and configure Advanced Space Combat ship cores")
            _G.language.Add("tool.asc_ship_core_tool.0", "Left click to spawn ship core, Right click to configure")
            return true
        end
        return false
    end)

    if not success then
        print("[ASC Ship Core Tool] Warning: Could not add language strings")
    end
end

function TOOL:LeftClick(trace)
    if CLIENT then return true end

    local ply = self:GetOwner()
    if not IsValid(ply) then return false end

    local ship_name = self:GetClientInfo("ship_name")
    local auto_link = self:GetClientNumber("auto_link", 1) == 1
    local power_level = self:GetClientNumber("power_level", 100)
    local enable_life_support = self:GetClientNumber("enable_life_support", 1) == 1
    local enable_shields = self:GetClientNumber("enable_shields", 1) == 1
    local enable_hull_system = self:GetClientNumber("enable_hull_system", 1) == 1

    if trace.HitSky then return false end

    -- Create ship core entity
    local ent = ents.Create("asc_ship_core")
    if not IsValid(ent) then
        ply:ChatPrint("Failed to create ASC ship core entity!")
        return false
    end

    ent:SetModel("models/props_combine/combine_core.mdl")
    ent:SetPos(trace.HitPos + trace.HitNormal * 50)
    ent:SetAngles(Angle(0, ply:EyeAngles().y + 180, 0))
    ent:Spawn()
    ent:Activate()

    -- Set ownership
    if ent.CPPISetOwner then
        ent:CPPISetOwner(ply)
    else
        ent:SetOwner(ply)
    end

    -- Configure ship core
    if ent.SetShipName then
        ent:SetShipName(ship_name)
    end

    -- Configure systems
    if ent.SetAutoLink then
        ent:SetAutoLink(auto_link)
    end

    if ent.SetPowerLevel then
        ent:SetPowerLevel(power_level)
    end

    -- Enable/disable systems based on tool settings
    if ent.SetLifeSupportEnabled then
        ent:SetLifeSupportEnabled(enable_life_support)
    end

    if ent.SetShieldsEnabled then
        ent:SetShieldsEnabled(enable_shields)
    end

    if ent.SetHullSystemEnabled then
        ent:SetHullSystemEnabled(enable_hull_system)
    end

    -- Add to undo
    undo.Create("ASC Ship Core")
    undo.AddEntity(ent)
    undo.SetPlayer(ply)
    undo.Finish()

    ply:ChatPrint("ASC ship core '" .. ship_name .. "' spawned")

    return true
end

function TOOL:RightClick(trace)
    if CLIENT then return true end

    local ply = self:GetOwner()
    local ent = trace.Entity

    if not IsValid(ent) or ent:GetClass() ~= "asc_ship_core" then
        ply:ChatPrint("Right click on an ASC ship core to configure it!")
        return false
    end

    -- Open ship core UI
    if ent.OpenUI then
        ent:OpenUI(ply)
        ply:ChatPrint("ASC ship core interface opened!")
    else
        ply:ChatPrint("ASC ship core configuration not available!")
    end

    return true
end

function TOOL:Reload(trace)
    if CLIENT then return true end

    local ply = self:GetOwner()
    local ent = trace.Entity

    if IsValid(ent) and ent:GetClass() == "asc_ship_core" then
        -- Show ship core information
        if ent.GetShipName then
            local shipName = ent:GetShipName() or "Unnamed Ship"
            ply:ChatPrint("Ship Core: " .. shipName)
        end

        if ent.GetStatus then
            local status = ent:GetStatus() or "Unknown"
            ply:ChatPrint("Status: " .. status)
        end
    else
        ply:ChatPrint("Reload: Look at an ASC ship core to view information!")
    end

    return true
end

if CLIENT then
    function TOOL.BuildCPanel(CPanel)
        CPanel:AddControl("Header", {
            Text = "ASC Ship Core Tool v6.0.0",
            Description = "Spawn and configure Advanced Space Combat ship cores"
        })

        -- Ship Configuration Section
        CPanel:AddControl("Label", {
            Text = "Ship Configuration:"
        })

        CPanel:AddControl("TextBox", {
            Label = "Ship Name",
            Command = "asc_ship_core_tool_ship_name",
            MaxLength = 32
        })

        CPanel:AddControl("Slider", {
            Label = "Power Level",
            Command = "asc_ship_core_tool_power_level",
            Type = "Integer",
            Min = 10,
            Max = 1000
        })

        -- System Configuration Section
        CPanel:AddControl("Label", {
            Text = "System Configuration:"
        })

        CPanel:AddControl("CheckBox", {
            Label = "Auto-link components",
            Command = "asc_ship_core_tool_auto_link"
        })

        CPanel:AddControl("CheckBox", {
            Label = "Enable life support",
            Command = "asc_ship_core_tool_enable_life_support"
        })

        CPanel:AddControl("CheckBox", {
            Label = "Enable shield system",
            Command = "asc_ship_core_tool_enable_shields"
        })

        CPanel:AddControl("CheckBox", {
            Label = "Enable hull damage system",
            Command = "asc_ship_core_tool_enable_hull_system"
        })

        -- Instructions Section
        CPanel:AddControl("Label", {
            Text = ""
        })

        CPanel:AddControl("Label", {
            Text = "Instructions:"
        })

        CPanel:AddControl("Label", {
            Text = "Left click: Spawn ASC ship core"
        })

        CPanel:AddControl("Label", {
            Text = "Right click: Open ship core configuration UI"
        })

        CPanel:AddControl("Label", {
            Text = "Reload: View ship core information"
        })
    end
end
